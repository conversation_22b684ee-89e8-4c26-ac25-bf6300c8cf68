import { useState, useCallback, useRef, useEffect } from 'react';

/**
 * State for async operations
 */
export interface AsyncState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

/**
 * Options for async operations
 */
export interface AsyncOptions {
  retryCount?: number;
  retryDelay?: number;
  timeout?: number;
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

/**
 * Result of useAsyncOperation hook
 */
export interface AsyncOperationResult<T> {
  state: AsyncState<T>;
  execute: (operation: () => Promise<T>) => Promise<void>;
  reset: () => void;
  retry: () => void;
}

/**
 * Enhanced hook for managing async operations with loading states, error handling, and retry logic
 * @param initialData Initial data value
 * @param options Configuration options
 * @returns Async operation state and control functions
 */
export function useAsyncOperation<T>(
  initialData: T | null = null,
  options: AsyncOptions = {}
): AsyncOperationResult<T> {
  const {
    retryCount = 3,
    retryDelay = 1000,
    timeout = 10000,
    onSuccess,
    onError
  } = options;

  const [state, setState] = useState<AsyncState<T>>({
    data: initialData,
    loading: false,
    error: null,
    lastUpdated: null
  });

  const lastOperationRef = useRef<(() => Promise<T>) | null>(null);
  const retryCountRef = useRef(0);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const execute = useCallback(async (operation: () => Promise<T>): Promise<void> => {
    lastOperationRef.current = operation;
    retryCountRef.current = 0;

    setState(prev => ({
      ...prev,
      loading: true,
      error: null
    }));

    const executeWithRetry = async (): Promise<void> => {
      try {
        // Set timeout for the operation
        const timeoutPromise = new Promise<never>((_, reject) => {
          timeoutRef.current = setTimeout(() => {
            reject(new Error(`Operation timed out after ${timeout}ms`));
          }, timeout);
        });

        const result = await Promise.race([operation(), timeoutPromise]);
        
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
          timeoutRef.current = null;
        }

        setState({
          data: result,
          loading: false,
          error: null,
          lastUpdated: new Date()
        });

        onSuccess?.();
      } catch (error) {
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
          timeoutRef.current = null;
        }

        const errorMessage = error instanceof Error ? error.message : String(error);
        
        if (retryCountRef.current < retryCount) {
          retryCountRef.current++;
          
          // Wait before retrying
          await new Promise(resolve => setTimeout(resolve, retryDelay));
          await executeWithRetry();
        } else {
          setState(prev => ({
            ...prev,
            loading: false,
            error: errorMessage
          }));

          onError?.(errorMessage);
        }
      }
    };

    await executeWithRetry();
  }, [retryCount, retryDelay, timeout, onSuccess, onError]);

  const reset = useCallback(() => {
    setState({
      data: initialData,
      loading: false,
      error: null,
      lastUpdated: null
    });
    lastOperationRef.current = null;
    retryCountRef.current = 0;
    
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, [initialData]);

  const retry = useCallback(async () => {
    if (lastOperationRef.current) {
      await execute(lastOperationRef.current);
    }
  }, [execute]);

  return {
    state,
    execute,
    reset,
    retry
  };
}

/**
 * Hook for managing multiple async operations
 */
export function useAsyncOperations<T extends Record<string, any>>(
  initialData: Partial<T> = {}
): {
  states: Record<keyof T, AsyncState<T[keyof T]>>;
  execute: <K extends keyof T>(key: K, operation: () => Promise<T[K]>) => Promise<void>;
  reset: (key?: keyof T) => void;
  isAnyLoading: boolean;
  hasAnyError: boolean;
} {
  const [states, setStates] = useState<Record<keyof T, AsyncState<T[keyof T]>>>(() => {
    const initial: Record<keyof T, AsyncState<T[keyof T]>> = {} as any;
    Object.keys(initialData).forEach(key => {
      initial[key as keyof T] = {
        data: initialData[key as keyof T] || null,
        loading: false,
        error: null,
        lastUpdated: null
      };
    });
    return initial;
  });

  const execute = useCallback(async <K extends keyof T>(
    key: K,
    operation: () => Promise<T[K]>
  ): Promise<void> => {
    setStates(prev => ({
      ...prev,
      [key]: {
        ...prev[key],
        loading: true,
        error: null
      }
    }));

    try {
      const result = await operation();
      setStates(prev => ({
        ...prev,
        [key]: {
          data: result,
          loading: false,
          error: null,
          lastUpdated: new Date()
        }
      }));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      setStates(prev => ({
        ...prev,
        [key]: {
          ...prev[key],
          loading: false,
          error: errorMessage
        }
      }));
    }
  }, []);

  const reset = useCallback((key?: keyof T) => {
    if (key) {
      setStates(prev => ({
        ...prev,
        [key]: {
          data: initialData[key] || null,
          loading: false,
          error: null,
          lastUpdated: null
        }
      }));
    } else {
      const resetStates: Record<keyof T, AsyncState<T[keyof T]>> = {} as any;
      Object.keys(states).forEach(k => {
        const key = k as keyof T;
        resetStates[key] = {
          data: initialData[key] || null,
          loading: false,
          error: null,
          lastUpdated: null
        };
      });
      setStates(resetStates);
    }
  }, [initialData, states]);

  const isAnyLoading = Object.values(states).some(state => state.loading);
  const hasAnyError = Object.values(states).some(state => state.error !== null);

  return {
    states,
    execute,
    reset,
    isAnyLoading,
    hasAnyError
  };
}
