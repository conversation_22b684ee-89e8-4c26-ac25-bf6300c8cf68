import React, { useState, useEffect } from 'react';
import { Guest, GuestFormData, GuestFormErrors } from '../../types';
import { guestService } from '../../services/guestService';
import Button from '../shared/Button';
import Input from '../shared/Input';
import Card from '../shared/Card';
import { SaveIcon, XIcon } from '../shared/Icon';

interface CustomerFormProps {
  customer?: Guest | undefined;
  onSave: (customer: Guest) => void;
  onCancel: () => void;
  isOpen: boolean;
}

const CustomerForm: React.FC<CustomerFormProps> = ({
  customer,
  onSave,
  onCancel,
  isOpen
}) => {
  const [formData, setFormData] = useState<GuestFormData>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    preferences: []
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<GuestFormErrors>({});

  useEffect(() => {
    if (customer) {
      setFormData({
        firstName: customer.firstName,
        lastName: customer.lastName,
        email: customer.email,
        phone: customer.phone,
        address: customer.address || '',
        preferences: customer.preferences || []
      });
    } else {
      setFormData({
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        address: '',
        preferences: []
      });
    }
    setErrors({});
  }, [customer, isOpen]);

  const validateForm = (): boolean => {
    const newErrors: GuestFormErrors = {};

    if (!formData.firstName.trim()) {
      newErrors.firstName = 'First name is required';
    }

    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Last name is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      const guestData: Guest = {
        id: customer?.id || `guest-${Date.now()}`,
        firstName: formData.firstName.trim(),
        lastName: formData.lastName.trim(),
        email: formData.email.trim(),
        phone: formData.phone.trim(),
        ...(formData.address?.trim() && { address: formData.address.trim() }),
        ...(formData.preferences && formData.preferences.length > 0 && { preferences: formData.preferences }),
        pastBookings: customer?.pastBookings || [],
        createdAt: customer?.createdAt || new Date(),
        updatedAt: new Date()
      };

      if (customer) {
        await guestService.updateGuest(guestData);
      } else {
        await guestService.addGuest(guestData);
      }

      onSave(guestData);
    } catch (error) {
      console.error('Error saving customer:', error);
      alert('Failed to save customer. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof GuestFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: undefined
      }));
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-navy rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
        <Card>
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-poppins font-semibold text-navy-dark dark:text-gold-accent">
              {customer ? 'Edit Customer' : 'Add New Customer'}
            </h2>
            <Button
              variant="secondary"
              onClick={onCancel}
              leftIcon={<XIcon className="w-4 h-4" />}
            >
              Close
            </Button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Input
                  label="First Name"
                  value={formData.firstName}
                  onChange={(e) => handleInputChange('firstName', e.target.value)}
                  error={errors.firstName}
                  required
                />
              </div>
              <div>
                <Input
                  label="Last Name"
                  value={formData.lastName}
                  onChange={(e) => handleInputChange('lastName', e.target.value)}
                  error={errors.lastName}
                  required
                />
              </div>
            </div>

            <Input
              label="Email"
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              error={errors.email}
              required
            />

            <Input
              label="Phone Number"
              type="tel"
              value={formData.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              error={errors.phone}
              required
            />

            <Input
              label="Address"
              value={formData.address || ''}
              onChange={(e) => handleInputChange('address', e.target.value)}
              placeholder="Optional"
            />

            <div className="flex justify-end space-x-3 pt-4">
              <Button
                type="button"
                variant="secondary"
                onClick={onCancel}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="primary"
                leftIcon={<SaveIcon className="w-4 h-4" />}
                disabled={loading}
              >
                {loading ? 'Saving...' : customer ? 'Update Customer' : 'Add Customer'}
              </Button>
            </div>
          </form>
        </Card>
      </div>
    </div>
  );
};

export default CustomerForm;
