import React, { useState, useEffect } from 'react';
import { Room, Booking, RoomStatus } from '../../types';
import { bookingService } from '../../services/bookingService';
import Button from '../shared/Button';
import Card from '../shared/Card';
import Badge from '../shared/Badge';
import { XIcon, EditIcon } from '../shared/Icon';

interface RoomDetailsProps {
  room: Room;
  onClose: () => void;
  onEdit: () => void;
  onUpdateStatus: (status: RoomStatus) => void;
  isOpen: boolean;
}

const RoomDetails: React.FC<RoomDetailsProps> = ({
  room,
  onClose,
  onEdit,
  onUpdateStatus,
  isOpen
}) => {
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (isOpen && room) {
      loadRoomBookings();
    }
  }, [isOpen, room]);

  const loadRoomBookings = async () => {
    try {
      setLoading(true);
      const roomBookings = await bookingService.getBookingsByRoom(room.id);
      // Sort by check-in date, most recent first
      roomBookings.sort((a, b) => new Date(b.checkInDate).getTime() - new Date(a.checkInDate).getTime());
      setBookings(roomBookings);
    } catch (error) {
      console.error('Error loading room bookings:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case RoomStatus.Available:
        return 'success';
      case RoomStatus.Occupied:
        return 'danger';
      case RoomStatus.Cleaning:
        return 'warning';
      case RoomStatus.OutOfService:
        return 'secondary';
      case RoomStatus.PendingArrival:
        return 'info';
      case RoomStatus.PendingDeparture:
        return 'info';
      default:
        return 'secondary';
    }
  };

  const getBookingStatusColor = (status: string) => {
    switch (status) {
      case 'Confirmed':
        return 'success';
      case 'Checked In':
        return 'info';
      case 'Checked Out':
        return 'secondary';
      case 'Cancelled':
        return 'danger';
      case 'Pending Payment':
        return 'warning';
      default:
        return 'secondary';
    }
  };

  const calculateTotalRevenue = () => {
    return bookings
      .filter(booking => booking.status === 'Checked Out')
      .reduce((total, booking) => total + booking.totalAmount, 0);
  };

  const getCurrentBooking = () => {
    return bookings.find(booking => 
      booking.status === 'Checked In' || 
      (booking.status === 'Confirmed' && room.currentBookingId === booking.id)
    );
  };

  const getStatusOptions = () => {
    return Object.values(RoomStatus).filter(status => status !== room.status);
  };

  if (!isOpen) return null;

  const currentBooking = getCurrentBooking();

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-navy rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <Card>
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-poppins font-semibold text-navy-dark dark:text-gold-accent">
              Room {room.roomNumber} Details
            </h2>
            <div className="flex space-x-2">
              <Button
                variant="primary"
                onClick={onEdit}
                leftIcon={<EditIcon className="w-4 h-4" />}
              >
                Edit
              </Button>
              <Button
                variant="secondary"
                onClick={onClose}
                leftIcon={<XIcon className="w-4 h-4" />}
              >
                Close
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Room Information */}
            <div className="lg:col-span-1">
              <Card>
                <div className="text-center mb-4">
                  <div className="h-20 w-20 rounded-lg bg-navy dark:bg-gold-accent flex items-center justify-center mx-auto mb-4">
                    <span className="text-2xl font-bold text-white">
                      {room.roomNumber}
                    </span>
                  </div>
                  <h3 className="text-xl font-semibold text-navy-dark dark:text-gold-accent">
                    {room.type.name}
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Floor {room.floor}
                  </p>
                </div>

                <div className="space-y-3 mb-6">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Status:</span>
                    <Badge variant={getStatusColor(room.status) as any}>
                      {room.status}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Base Price:</span>
                    <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {formatCurrency(room.type.basePrice)}/night
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Capacity:</span>
                    <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {room.type.capacity} guest{room.type.capacity !== 1 ? 's' : ''}
                    </span>
                  </div>
                </div>

                {/* Status Update */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Update Status
                  </label>
                  <select
                    className="w-full px-3 py-2 border border-gray-300 dark:border-navy-light rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gold-accent focus:border-transparent bg-white dark:bg-navy-light text-gray-900 dark:text-gray-100"
                    onChange={(e) => onUpdateStatus(e.target.value as RoomStatus)}
                    value=""
                  >
                    <option value="">Select new status</option>
                    {getStatusOptions().map((status) => (
                      <option key={status} value={status}>
                        {status}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Amenities */}
                <div className="mb-6">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                    Amenities
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {room.type.amenities.map((amenity, index) => (
                      <Badge key={index} variant="info">
                        {amenity}
                      </Badge>
                    ))}
                  </div>
                </div>

                {room.notes && (
                  <div className="mb-6">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                      Notes
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {room.notes}
                    </p>
                  </div>
                )}

                <div className="pt-6 border-t border-gray-200 dark:border-navy-light">
                  <div className="grid grid-cols-2 gap-4 text-center">
                    <div>
                      <div className="text-2xl font-bold text-navy-dark dark:text-gold-accent">
                        {bookings.length}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        Total Bookings
                      </div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-navy-dark dark:text-gold-accent">
                        {formatCurrency(calculateTotalRevenue())}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        Total Revenue
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            </div>

            {/* Current Booking & History */}
            <div className="lg:col-span-2">
              {/* Current Booking */}
              {currentBooking && (
                <Card className="mb-6">
                  <h3 className="text-lg font-semibold text-navy-dark dark:text-gold-accent mb-4">
                    Current Booking
                  </h3>
                  <div className="border border-gray-200 dark:border-navy-light rounded-lg p-4">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h4 className="font-medium text-gray-900 dark:text-gray-100">
                          {currentBooking.guest.firstName} {currentBooking.guest.lastName}
                        </h4>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {currentBooking.guest.email}
                        </p>
                      </div>
                      <Badge variant={getBookingStatusColor(currentBooking.status) as any}>
                        {currentBooking.status}
                      </Badge>
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500 dark:text-gray-400">Check-in:</span>
                        <div className="font-medium">{formatDate(currentBooking.checkInDate)}</div>
                      </div>
                      <div>
                        <span className="text-gray-500 dark:text-gray-400">Check-out:</span>
                        <div className="font-medium">{formatDate(currentBooking.checkOutDate)}</div>
                      </div>
                      <div>
                        <span className="text-gray-500 dark:text-gray-400">Guests:</span>
                        <div className="font-medium">{currentBooking.numberOfGuests}</div>
                      </div>
                      <div>
                        <span className="text-gray-500 dark:text-gray-400">Total:</span>
                        <div className="font-medium">{formatCurrency(currentBooking.totalAmount)}</div>
                      </div>
                    </div>
                  </div>
                </Card>
              )}

              {/* Booking History */}
              <Card>
                <h3 className="text-lg font-semibold text-navy-dark dark:text-gold-accent mb-4">
                  Booking History
                </h3>

                {loading ? (
                  <div className="flex items-center justify-center h-32">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  </div>
                ) : bookings.length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-gray-500 dark:text-gray-400">
                      No bookings found for this room.
                    </p>
                  </div>
                ) : (
                  <div className="space-y-4 max-h-96 overflow-y-auto">
                    {bookings.map((booking) => (
                      <div
                        key={booking.id}
                        className="border border-gray-200 dark:border-navy-light rounded-lg p-4"
                      >
                        <div className="flex justify-between items-start mb-2">
                          <div>
                            <h4 className="font-medium text-gray-900 dark:text-gray-100">
                              {booking.guest.firstName} {booking.guest.lastName}
                            </h4>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              Booking ID: {booking.id}
                            </p>
                          </div>
                          <Badge variant={getBookingStatusColor(booking.status) as any}>
                            {booking.status}
                          </Badge>
                        </div>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <span className="text-gray-500 dark:text-gray-400">Check-in:</span>
                            <div className="font-medium">{formatDate(booking.checkInDate)}</div>
                          </div>
                          <div>
                            <span className="text-gray-500 dark:text-gray-400">Check-out:</span>
                            <div className="font-medium">{formatDate(booking.checkOutDate)}</div>
                          </div>
                          <div>
                            <span className="text-gray-500 dark:text-gray-400">Guests:</span>
                            <div className="font-medium">{booking.numberOfGuests}</div>
                          </div>
                          <div>
                            <span className="text-gray-500 dark:text-gray-400">Total:</span>
                            <div className="font-medium">{formatCurrency(booking.totalAmount)}</div>
                          </div>
                        </div>
                        {booking.notes && (
                          <div className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                            <span className="font-medium">Notes:</span> {booking.notes}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </Card>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default RoomDetails;
