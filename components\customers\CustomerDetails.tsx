import React, { useState, useEffect } from 'react';
import { Guest, Booking } from '../../types';
import { bookingService } from '../../services/bookingService';
import Button from '../shared/Button';
import Card from '../shared/Card';
import Badge from '../shared/Badge';
import { XIcon, EditIcon, CalendarIcon, PhoneIcon, MailIcon, MapPinIcon } from '../shared/Icon';

interface CustomerDetailsProps {
  customer: Guest;
  onClose: () => void;
  onEdit: () => void;
  isOpen: boolean;
}

const CustomerDetails: React.FC<CustomerDetailsProps> = ({
  customer,
  onClose,
  onEdit,
  isOpen
}) => {
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (isOpen && customer) {
      loadCustomerBookings();
    }
  }, [isOpen, customer]);

  const loadCustomerBookings = async () => {
    try {
      setLoading(true);
      const customerBookings = await bookingService.getBookingsByGuest(customer.id);
      setBookings(customerBookings);
    } catch (error) {
      console.error('Error loading customer bookings:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Confirmed':
        return 'success';
      case 'Checked In':
        return 'info';
      case 'Checked Out':
        return 'secondary';
      case 'Cancelled':
        return 'danger';
      case 'Pending Payment':
        return 'warning';
      default:
        return 'secondary';
    }
  };

  const calculateTotalRevenue = () => {
    return bookings
      .filter(booking => booking.status === 'Checked Out')
      .reduce((total, booking) => total + booking.totalAmount, 0);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-navy rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <Card>
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-poppins font-semibold text-navy-dark dark:text-gold-accent">
              Customer Details
            </h2>
            <div className="flex space-x-2">
              <Button
                variant="primary"
                onClick={onEdit}
                leftIcon={<EditIcon className="w-4 h-4" />}
              >
                Edit
              </Button>
              <Button
                variant="secondary"
                onClick={onClose}
                leftIcon={<XIcon className="w-4 h-4" />}
              >
                Close
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Customer Information */}
            <div className="lg:col-span-1">
              <Card>
                <div className="text-center mb-4">
                  <div className="h-20 w-20 rounded-full bg-gold-accent flex items-center justify-center mx-auto mb-4">
                    <span className="text-2xl font-bold text-white">
                      {customer.firstName.charAt(0)}{customer.lastName.charAt(0)}
                    </span>
                  </div>
                  <h3 className="text-xl font-semibold text-navy-dark dark:text-gold-accent">
                    {customer.firstName} {customer.lastName}
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Customer ID: {customer.id}
                  </p>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <MailIcon className="w-5 h-5 text-gray-400" />
                    <span className="text-sm text-gray-700 dark:text-gray-300">
                      {customer.email}
                    </span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <PhoneIcon className="w-5 h-5 text-gray-400" />
                    <span className="text-sm text-gray-700 dark:text-gray-300">
                      {customer.phone}
                    </span>
                  </div>
                  {customer.address && (
                    <div className="flex items-center space-x-3">
                      <MapPinIcon className="w-5 h-5 text-gray-400" />
                      <span className="text-sm text-gray-700 dark:text-gray-300">
                        {customer.address}
                      </span>
                    </div>
                  )}
                  <div className="flex items-center space-x-3">
                    <CalendarIcon className="w-5 h-5 text-gray-400" />
                    <span className="text-sm text-gray-700 dark:text-gray-300">
                      Joined: {formatDate(customer.createdAt)}
                    </span>
                  </div>
                </div>

                {customer.preferences && customer.preferences.length > 0 && (
                  <div className="mt-6">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                      Preferences
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {customer.preferences.map((preference, index) => (
                        <Badge key={index} variant="info">
                          {preference}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                <div className="mt-6 pt-6 border-t border-gray-200 dark:border-navy-light">
                  <div className="grid grid-cols-2 gap-4 text-center">
                    <div>
                      <div className="text-2xl font-bold text-navy-dark dark:text-gold-accent">
                        {bookings.length}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        Total Bookings
                      </div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-navy-dark dark:text-gold-accent">
                        {formatCurrency(calculateTotalRevenue())}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        Total Revenue
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            </div>

            {/* Booking History */}
            <div className="lg:col-span-2">
              <Card>
                <h3 className="text-lg font-semibold text-navy-dark dark:text-gold-accent mb-4">
                  Booking History
                </h3>

                {loading ? (
                  <div className="flex items-center justify-center h-32">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  </div>
                ) : bookings.length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-gray-500 dark:text-gray-400">
                      No bookings found for this customer.
                    </p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {bookings.map((booking) => (
                      <div
                        key={booking.id}
                        className="border border-gray-200 dark:border-navy-light rounded-lg p-4"
                      >
                        <div className="flex justify-between items-start mb-2">
                          <div>
                            <h4 className="font-medium text-gray-900 dark:text-gray-100">
                              Room {booking.room.roomNumber} - {booking.room.type.name}
                            </h4>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              Booking ID: {booking.id}
                            </p>
                          </div>
                          <Badge variant={getStatusColor(booking.status) as any}>
                            {booking.status}
                          </Badge>
                        </div>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <span className="text-gray-500 dark:text-gray-400">Check-in:</span>
                            <div className="font-medium">{formatDate(booking.checkInDate)}</div>
                          </div>
                          <div>
                            <span className="text-gray-500 dark:text-gray-400">Check-out:</span>
                            <div className="font-medium">{formatDate(booking.checkOutDate)}</div>
                          </div>
                          <div>
                            <span className="text-gray-500 dark:text-gray-400">Guests:</span>
                            <div className="font-medium">{booking.numberOfGuests}</div>
                          </div>
                          <div>
                            <span className="text-gray-500 dark:text-gray-400">Total:</span>
                            <div className="font-medium">{formatCurrency(booking.totalAmount)}</div>
                          </div>
                        </div>
                        {booking.notes && (
                          <div className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                            <span className="font-medium">Notes:</span> {booking.notes}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </Card>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default CustomerDetails;
