// Types are imported when needed in specific methods

const DB_NAME = 'HotelManagementDB';
const DB_VERSION = 1;

// Store names
export const STORES = {
  ROOM_TYPES: 'roomTypes',
  ROOMS: 'rooms',
  GUESTS: 'guests',
  BOOKINGS: 'bookings',
  HOUSEKEEPING_TASKS: 'housekeepingTasks',
  STAFF: 'staff',
  SETTINGS: 'settings'
} as const;

class DatabaseManager {
  private db: IDBDatabase | null = null;

  async init(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(DB_NAME, DB_VERSION);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;

        // Create Room Types store
        if (!db.objectStoreNames.contains(STORES.ROOM_TYPES)) {
          const roomTypesStore = db.createObjectStore(STORES.ROOM_TYPES, { keyPath: 'id' });
          roomTypesStore.createIndex('name', 'name', { unique: false });
        }

        // Create Rooms store
        if (!db.objectStoreNames.contains(STORES.ROOMS)) {
          const roomsStore = db.createObjectStore(STORES.ROOMS, { keyPath: 'id' });
          roomsStore.createIndex('roomNumber', 'roomNumber', { unique: true });
          roomsStore.createIndex('status', 'status', { unique: false });
          roomsStore.createIndex('floor', 'floor', { unique: false });
        }

        // Create Guests store
        if (!db.objectStoreNames.contains(STORES.GUESTS)) {
          const guestsStore = db.createObjectStore(STORES.GUESTS, { keyPath: 'id' });
          guestsStore.createIndex('email', 'email', { unique: true });
          guestsStore.createIndex('phone', 'phone', { unique: false });
          guestsStore.createIndex('lastName', 'lastName', { unique: false });
        }

        // Create Bookings store
        if (!db.objectStoreNames.contains(STORES.BOOKINGS)) {
          const bookingsStore = db.createObjectStore(STORES.BOOKINGS, { keyPath: 'id' });
          bookingsStore.createIndex('guestId', 'guestId', { unique: false });
          bookingsStore.createIndex('roomId', 'roomId', { unique: false });
          bookingsStore.createIndex('status', 'status', { unique: false });
          bookingsStore.createIndex('checkInDate', 'checkInDate', { unique: false });
          bookingsStore.createIndex('checkOutDate', 'checkOutDate', { unique: false });
        }

        // Create Housekeeping Tasks store
        if (!db.objectStoreNames.contains(STORES.HOUSEKEEPING_TASKS)) {
          const tasksStore = db.createObjectStore(STORES.HOUSEKEEPING_TASKS, { keyPath: 'id' });
          tasksStore.createIndex('roomId', 'roomId', { unique: false });
          tasksStore.createIndex('status', 'status', { unique: false });
          tasksStore.createIndex('priority', 'priority', { unique: false });
          tasksStore.createIndex('assignedTo', 'assignedTo', { unique: false });
        }

        // Create Staff store
        if (!db.objectStoreNames.contains(STORES.STAFF)) {
          const staffStore = db.createObjectStore(STORES.STAFF, { keyPath: 'id' });
          staffStore.createIndex('email', 'email', { unique: true });
          staffStore.createIndex('role', 'role', { unique: false });
        }

        // Create Settings store
        if (!db.objectStoreNames.contains(STORES.SETTINGS)) {
          db.createObjectStore(STORES.SETTINGS, { keyPath: 'key' });
        }
      };
    });
  }

  private getStore(storeName: string, mode: 'readonly' | 'readwrite' = 'readonly'): IDBObjectStore {
    if (!this.db) {
      throw new Error('Database not initialized');
    }
    const transaction = this.db.transaction([storeName], mode);
    return transaction.objectStore(storeName);
  }

  // Generic CRUD operations
  async add<T>(storeName: string, data: T): Promise<void> {
    return new Promise((resolve, reject) => {
      const store = this.getStore(storeName, 'readwrite');
      const request = store.add(data);
      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  async update<T>(storeName: string, data: T): Promise<void> {
    return new Promise((resolve, reject) => {
      const store = this.getStore(storeName, 'readwrite');
      const request = store.put(data);
      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  async get<T>(storeName: string, id: string): Promise<T | undefined> {
    return new Promise((resolve, reject) => {
      const store = this.getStore(storeName);
      const request = store.get(id);
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  async getAll<T>(storeName: string): Promise<T[]> {
    return new Promise((resolve, reject) => {
      const store = this.getStore(storeName);
      const request = store.getAll();
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  async delete(storeName: string, id: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const store = this.getStore(storeName, 'readwrite');
      const request = store.delete(id);
      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  async clear(storeName: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const store = this.getStore(storeName, 'readwrite');
      const request = store.clear();
      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  async getByIndex<T>(storeName: string, indexName: string, value: string | number): Promise<T[]> {
    return new Promise((resolve, reject) => {
      const store = this.getStore(storeName);
      const index = store.index(indexName);
      const request = index.getAll(value);
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }
}

// Singleton instance
export const db = new DatabaseManager();

// Initialize database
export const initDatabase = async (): Promise<void> => {
  await db.init();
};
