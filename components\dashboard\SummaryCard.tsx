
import React from 'react';
import Card from '../shared/Card';

interface SummaryCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  trend?: string; // e.g., "+5% vs last week"
  trendColor?: 'green' | 'red' | 'gray';
  bgColorClass?: string; // Tailwind bg color for icon container
  textColorClass?: string; // Tailwind text color for icon
}

const SummaryCard: React.FC<SummaryCardProps> = ({ title, value, icon, trend, trendColor = 'gray', bgColorClass = 'bg-navy-light', textColorClass = 'text-gold-accent' }) => {
  const trendColorClasses = {
    green: 'text-status-green',
    red: 'text-status-red',
    gray: 'text-gray-500 dark:text-gray-400',
  };

  return (
    <Card className="transform hover:scale-105 transition-transform duration-300">
      <div className="flex items-center justify-between">
        <div className={`p-3 rounded-lg ${bgColorClass} mr-4`}>
          <div className={`w-7 h-7 ${textColorClass}`}>
            {icon}
          </div>
        </div>
        <div className="text-right">
          <p className="text-sm text-gray-500 dark:text-gray-400 font-medium">{title}</p>
          <p className="text-3xl font-poppins font-semibold text-navy-dark dark:text-white">{value}</p>
        </div>
      </div>
      {trend && (
        <p className={`text-xs mt-2 text-right ${trendColorClasses[trendColor]}`}>{trend}</p>
      )}
    </Card>
  );
};

export default SummaryCard;
