
import React from 'react';
import { UserRole } from '../types';
import Card from '../components/shared/Card';
import OccupancyChart from '../components/dashboard/OccupancyChart'; // Reusing for demo

interface ReportsPageProps {
  userRole: UserRole;
}

const ReportsPage: React.FC<ReportsPageProps> = ({ userRole }) => {
  if (userRole !== UserRole.Admin && userRole !== UserRole.Manager) {
    return (
      <Card>
        <p className="text-center text-status-red p-8">You do not have permission to view this page.</p>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-poppins font-semibold text-navy-dark dark:text-gold-accent">Reports & Analytics</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card title="Overall Occupancy Trends">
          {/* Placeholder for a more detailed occupancy report chart */}
          <OccupancyChart /> 
          <p className="mt-4 text-sm text-gray-600 dark:text-gray-300">Detailed daily, weekly, and monthly occupancy data will be shown here.</p>
        </Card>
        
        <Card title="Revenue Analysis">
          {/* Placeholder for revenue charts */}
          <div className="h-64 flex items-center justify-center bg-light-gray dark:bg-navy-light rounded-lg">
            <p className="text-gray-500 dark:text-gray-400">Revenue charts (by room type, period) coming soon.</p>
          </div>
          <p className="mt-4 text-sm text-gray-600 dark:text-gray-300">Analysis of revenue sources and trends.</p>
        </Card>
      </div>

      <Card title="Staff Performance (Manager View)">
         {/* Placeholder for staff performance metrics */}
         <div className="h-48 flex items-center justify-center bg-light-gray dark:bg-navy-light rounded-lg">
            <p className="text-gray-500 dark:text-gray-400">Staff performance metrics will be displayed here for managers.</p>
          </div>
      </Card>

      <Card title="Custom Report Generator">
        <p className="text-gray-600 dark:text-gray-300">
          Future functionality: Allow users to generate custom reports based on selected criteria.
        </p>
        {/* Placeholder for custom report generation tools */}
      </Card>
    </div>
  );
};

export default ReportsPage;
