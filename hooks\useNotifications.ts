import { useState, useCallback, useRef, useEffect } from 'react';

/**
 * Notification types
 */
export const NotificationType = {
  Success: 'success',
  Error: 'error',
  Warning: 'warning',
  Info: 'info',
} as const;

export type NotificationType = typeof NotificationType[keyof typeof NotificationType];

/**
 * Notification interface
 */
export interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  message?: string;
  duration?: number;
  persistent?: boolean;
  action?: {
    label: string;
    onClick: () => void;
  };
  createdAt: Date;
}

/**
 * Notification options
 */
export interface NotificationOptions {
  duration?: number;
  persistent?: boolean;
  action?: {
    label: string;
    onClick: () => void;
  };
}

/**
 * Default notification durations (in milliseconds)
 */
const DEFAULT_DURATIONS = {
  [NotificationType.Success]: 4000,
  [NotificationType.Error]: 6000,
  [NotificationType.Warning]: 5000,
  [NotificationType.Info]: 4000,
};

/**
 * Enhanced notifications hook for user feedback
 * @param maxNotifications Maximum number of notifications to show at once
 * @returns Notification state and control functions
 */
export function useNotifications(maxNotifications = 5) {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const timeoutsRef = useRef<Map<string, NodeJS.Timeout>>(new Map());

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      timeoutsRef.current.forEach(timeout => clearTimeout(timeout));
      timeoutsRef.current.clear();
    };
  }, []);

  /**
   * Generate unique notification ID
   */
  const generateId = useCallback(() => {
    return `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }, []);

  /**
   * Remove notification by ID
   */
  const removeNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
    
    // Clear timeout if exists
    const timeout = timeoutsRef.current.get(id);
    if (timeout) {
      clearTimeout(timeout);
      timeoutsRef.current.delete(id);
    }
  }, []);

  /**
   * Add a new notification
   */
  const addNotification = useCallback((
    type: NotificationType,
    title: string,
    message?: string,
    options?: NotificationOptions
  ): string => {
    const id = generateId();
    const duration = options?.duration ?? DEFAULT_DURATIONS[type];
    const persistent = options?.persistent ?? false;

    const notification: Notification = {
      id,
      type,
      title,
      message,
      duration,
      persistent,
      action: options?.action,
      createdAt: new Date()
    };

    setNotifications(prev => {
      const newNotifications = [notification, ...prev];
      
      // Limit the number of notifications
      if (newNotifications.length > maxNotifications) {
        const removed = newNotifications.slice(maxNotifications);
        removed.forEach(n => {
          const timeout = timeoutsRef.current.get(n.id);
          if (timeout) {
            clearTimeout(timeout);
            timeoutsRef.current.delete(n.id);
          }
        });
        return newNotifications.slice(0, maxNotifications);
      }
      
      return newNotifications;
    });

    // Set auto-remove timeout if not persistent
    if (!persistent && duration > 0) {
      const timeout = setTimeout(() => {
        removeNotification(id);
      }, duration);
      
      timeoutsRef.current.set(id, timeout);
    }

    return id;
  }, [generateId, removeNotification, maxNotifications]);

  /**
   * Convenience methods for different notification types
   */
  const success = useCallback((
    title: string,
    message?: string,
    options?: NotificationOptions
  ) => addNotification(NotificationType.Success, title, message, options), [addNotification]);

  const error = useCallback((
    title: string,
    message?: string,
    options?: NotificationOptions
  ) => addNotification(NotificationType.Error, title, message, options), [addNotification]);

  const warning = useCallback((
    title: string,
    message?: string,
    options?: NotificationOptions
  ) => addNotification(NotificationType.Warning, title, message, options), [addNotification]);

  const info = useCallback((
    title: string,
    message?: string,
    options?: NotificationOptions
  ) => addNotification(NotificationType.Info, title, message, options), [addNotification]);

  /**
   * Clear all notifications
   */
  const clearAll = useCallback(() => {
    // Clear all timeouts
    timeoutsRef.current.forEach(timeout => clearTimeout(timeout));
    timeoutsRef.current.clear();
    
    setNotifications([]);
  }, []);

  /**
   * Clear notifications by type
   */
  const clearByType = useCallback((type: NotificationType) => {
    setNotifications(prev => {
      const toRemove = prev.filter(n => n.type === type);
      toRemove.forEach(n => {
        const timeout = timeoutsRef.current.get(n.id);
        if (timeout) {
          clearTimeout(timeout);
          timeoutsRef.current.delete(n.id);
        }
      });
      
      return prev.filter(n => n.type !== type);
    });
  }, []);

  /**
   * Update notification
   */
  const updateNotification = useCallback((
    id: string,
    updates: Partial<Pick<Notification, 'title' | 'message' | 'type'>>
  ) => {
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === id
          ? { ...notification, ...updates }
          : notification
      )
    );
  }, []);

  /**
   * Pause auto-removal for a notification
   */
  const pauseNotification = useCallback((id: string) => {
    const timeout = timeoutsRef.current.get(id);
    if (timeout) {
      clearTimeout(timeout);
      timeoutsRef.current.delete(id);
    }
  }, []);

  /**
   * Resume auto-removal for a notification
   */
  const resumeNotification = useCallback((id: string, duration?: number) => {
    const notification = notifications.find(n => n.id === id);
    if (notification && !notification.persistent) {
      const timeoutDuration = duration ?? notification.duration ?? DEFAULT_DURATIONS[notification.type];
      
      const timeout = setTimeout(() => {
        removeNotification(id);
      }, timeoutDuration);
      
      timeoutsRef.current.set(id, timeout);
    }
  }, [notifications, removeNotification]);

  return {
    // State
    notifications,
    
    // Actions
    addNotification,
    removeNotification,
    clearAll,
    clearByType,
    updateNotification,
    pauseNotification,
    resumeNotification,
    
    // Convenience methods
    success,
    error,
    warning,
    info
  };
}

/**
 * Hook for handling global error boundaries
 */
export function useErrorHandler() {
  const { error } = useNotifications();

  const handleError = useCallback((
    err: Error | string,
    context?: string,
    options?: NotificationOptions
  ) => {
    const message = err instanceof Error ? err.message : err;
    const title = context ? `Error in ${context}` : 'An error occurred';
    
    console.error('Error handled:', { error: err, context });
    
    error(title, message, {
      persistent: true,
      ...options
    });
  }, [error]);

  const handleAsyncError = useCallback(async <T>(
    operation: () => Promise<T>,
    context?: string,
    options?: NotificationOptions
  ): Promise<T | null> => {
    try {
      return await operation();
    } catch (err) {
      handleError(err instanceof Error ? err : new Error(String(err)), context, options);
      return null;
    }
  }, [handleError]);

  return {
    handleError,
    handleAsyncError
  };
}
