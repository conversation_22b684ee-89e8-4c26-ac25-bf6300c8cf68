import { Booking, BookingStatus, RoomStatus } from '../types';
import { db, STORES, DatabaseResult, DatabaseError } from '../lib/database';
import { roomService } from './roomService';
import { guestService } from './guestService';

// Interface for storing bookings in IndexedDB (with IDs instead of full objects)
interface BookingData {
  id: string;
  guestId: string;
  roomId: string;
  checkInDate: Date;
  checkOutDate: Date;
  numberOfGuests: number;
  totalAmount: number;
  status: BookingStatus;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Enhanced Booking Service with comprehensive error handling and type safety
 */
export class BookingService {
  /**
   * Add a new booking with enhanced error handling and validation
   */
  async addBooking(booking: Booking): Promise<DatabaseResult<Booking>> {
    try {
      // Validate booking dates
      if (booking.checkInDate >= booking.checkOutDate) {
        return { success: false, error: 'Check-in date must be before check-out date' };
      }

      // Check if room is available for the requested dates
      const roomAvailabilityResult = await this.isRoomAvailable(
        booking.room.id,
        booking.checkInDate,
        booking.checkOutDate
      );

      if (!roomAvailabilityResult.success || !roomAvailabilityResult.data) {
        return { success: false, error: 'Room is not available for the selected dates' };
      }

      const bookingData: BookingData = {
        id: booking.id,
        guestId: booking.guest.id,
        roomId: booking.room.id,
        checkInDate: booking.checkInDate,
        checkOutDate: booking.checkOutDate,
        numberOfGuests: booking.numberOfGuests,
        totalAmount: booking.totalAmount,
        status: booking.status,
        ...(booking.notes && { notes: booking.notes }),
        createdAt: booking.createdAt,
        updatedAt: new Date()
      };

      const result = await db.add(STORES.BOOKINGS, bookingData);
      if (!result.success) {
        return result;
      }

      // Update room status if booking is confirmed or checked in
      if (booking.status === BookingStatus.Confirmed || booking.status === BookingStatus.CheckedIn) {
        const newRoomStatus = booking.status === BookingStatus.CheckedIn ? RoomStatus.Occupied : RoomStatus.PendingArrival;
        const roomUpdateResult = await roomService.updateRoomStatus(booking.room.id, newRoomStatus, booking.id);

        if (!roomUpdateResult.success) {
          // Rollback booking if room update fails
          await db.delete(STORES.BOOKINGS, booking.id);
          return { success: false, error: 'Failed to update room status' };
        }
      }

      return { success: true, data: booking };
    } catch (error) {
      return {
        success: false,
        error: error instanceof DatabaseError ? error.message : 'Failed to add booking'
      };
    }
  }

  async updateBooking(booking: Booking): Promise<void> {
    const bookingData: BookingData = {
      id: booking.id,
      guestId: booking.guest.id,
      roomId: booking.room.id,
      checkInDate: booking.checkInDate,
      checkOutDate: booking.checkOutDate,
      numberOfGuests: booking.numberOfGuests,
      totalAmount: booking.totalAmount,
      status: booking.status,
      ...(booking.notes && { notes: booking.notes }),
      createdAt: booking.createdAt,
      updatedAt: new Date()
    };
    
    await db.update(STORES.BOOKINGS, bookingData);
  }

  async getBooking(id: string): Promise<Booking | undefined> {
    const bookingData = await db.get<BookingData>(STORES.BOOKINGS, id);
    if (!bookingData) return undefined;
    
    return await this.populateBooking(bookingData);
  }

  async getAllBookings(): Promise<Booking[]> {
    const bookingsData = await db.getAll<BookingData>(STORES.BOOKINGS);
    const bookings: Booking[] = [];
    
    for (const bookingData of bookingsData) {
      const booking = await this.populateBooking(bookingData);
      if (booking) {
        bookings.push(booking);
      }
    }
    
    return bookings;
  }

  async deleteBooking(id: string): Promise<void> {
    const booking = await this.getBooking(id);
    if (booking) {
      // Update room status to available
      await roomService.updateRoomStatus(booking.room.id, RoomStatus.Available);
    }
    await db.delete(STORES.BOOKINGS, id);
  }

  async getBookingsByGuest(guestId: string): Promise<Booking[]> {
    const bookingsData = await db.getByIndex<BookingData>(STORES.BOOKINGS, 'guestId', guestId);
    const bookings: Booking[] = [];
    
    for (const bookingData of bookingsData) {
      const booking = await this.populateBooking(bookingData);
      if (booking) {
        bookings.push(booking);
      }
    }
    
    return bookings;
  }

  async getBookingsByRoom(roomId: string): Promise<Booking[]> {
    const bookingsData = await db.getByIndex<BookingData>(STORES.BOOKINGS, 'roomId', roomId);
    const bookings: Booking[] = [];
    
    for (const bookingData of bookingsData) {
      const booking = await this.populateBooking(bookingData);
      if (booking) {
        bookings.push(booking);
      }
    }
    
    return bookings;
  }

  async getBookingsByStatus(status: BookingStatus): Promise<Booking[]> {
    const bookingsData = await db.getByIndex<BookingData>(STORES.BOOKINGS, 'status', status);
    const bookings: Booking[] = [];
    
    for (const bookingData of bookingsData) {
      const booking = await this.populateBooking(bookingData);
      if (booking) {
        bookings.push(booking);
      }
    }
    
    return bookings;
  }

  async checkInGuest(bookingId: string): Promise<void> {
    const booking = await this.getBooking(bookingId);
    if (booking && booking.status === BookingStatus.Confirmed) {
      booking.status = BookingStatus.CheckedIn;
      await this.updateBooking(booking);
      await roomService.updateRoomStatus(booking.room.id, RoomStatus.Occupied, bookingId);
    }
  }

  async checkOutGuest(bookingId: string): Promise<void> {
    const booking = await this.getBooking(bookingId);
    if (booking && booking.status === BookingStatus.CheckedIn) {
      booking.status = BookingStatus.CheckedOut;
      await this.updateBooking(booking);
      await roomService.updateRoomStatus(booking.room.id, RoomStatus.Cleaning);
      
      // Add booking to guest history
      await guestService.addBookingToGuestHistory(booking.guest.id, bookingId);
    }
  }

  async cancelBooking(bookingId: string): Promise<void> {
    const booking = await this.getBooking(bookingId);
    if (booking) {
      booking.status = BookingStatus.Cancelled;
      await this.updateBooking(booking);
      await roomService.updateRoomStatus(booking.room.id, RoomStatus.Available);
    }
  }

  async getTodaysArrivals(): Promise<Booking[]> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    const allBookings = await this.getAllBookings();
    return allBookings.filter(booking => {
      const checkInDate = new Date(booking.checkInDate);
      checkInDate.setHours(0, 0, 0, 0);
      return checkInDate.getTime() === today.getTime() && 
             (booking.status === BookingStatus.Confirmed || booking.status === BookingStatus.Pending);
    });
  }

  async getTodaysDepartures(): Promise<Booking[]> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const allBookings = await this.getAllBookings();
    return allBookings.filter(booking => {
      const checkOutDate = new Date(booking.checkOutDate);
      checkOutDate.setHours(0, 0, 0, 0);
      return checkOutDate.getTime() === today.getTime() && 
             booking.status === BookingStatus.CheckedIn;
    });
  }

  private async populateBooking(bookingData: BookingData): Promise<Booking | undefined> {
    const guest = await guestService.getGuest(bookingData.guestId);
    const room = await roomService.getRoom(bookingData.roomId);
    
    if (!guest || !room) return undefined;
    
    return {
      id: bookingData.id,
      guest,
      room,
      checkInDate: bookingData.checkInDate,
      checkOutDate: bookingData.checkOutDate,
      numberOfGuests: bookingData.numberOfGuests,
      totalAmount: bookingData.totalAmount,
      status: bookingData.status,
      ...(bookingData.notes && { notes: bookingData.notes }),
      createdAt: bookingData.createdAt,
      updatedAt: bookingData.updatedAt
    };
  }

  async getBookingStats(): Promise<{
    total: number;
    confirmed: number;
    checkedIn: number;
    checkedOut: number;
    cancelled: number;
    pending: number;
    revenue: number;
  }> {
    const allBookings = await this.getAllBookings();

    return {
      total: allBookings.length,
      confirmed: allBookings.filter(b => b.status === BookingStatus.Confirmed).length,
      checkedIn: allBookings.filter(b => b.status === BookingStatus.CheckedIn).length,
      checkedOut: allBookings.filter(b => b.status === BookingStatus.CheckedOut).length,
      cancelled: allBookings.filter(b => b.status === BookingStatus.Cancelled).length,
      pending: allBookings.filter(b => b.status === BookingStatus.Pending).length,
      revenue: allBookings
        .filter(b => b.status === BookingStatus.CheckedOut)
        .reduce((sum, b) => sum + b.totalAmount, 0)
    };
  }

  /**
   * Get today's arrivals
   */
  async getTodaysArrivals(): Promise<DatabaseResult<Booking[]>> {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      const allBookingsResult = await this.getAllBookings();
      if (!allBookingsResult.success || !allBookingsResult.data) {
        return { success: false, error: 'Failed to get bookings' };
      }

      const todaysArrivals = allBookingsResult.data.filter(booking => {
        const checkInDate = new Date(booking.checkInDate);
        return checkInDate >= today && checkInDate < tomorrow;
      });

      return { success: true, data: todaysArrivals };
    } catch (error) {
      return {
        success: false,
        error: error instanceof DatabaseError ? error.message : 'Failed to get today\'s arrivals'
      };
    }
  }

  /**
   * Get today's departures
   */
  async getTodaysDepartures(): Promise<DatabaseResult<Booking[]>> {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      const allBookingsResult = await this.getAllBookings();
      if (!allBookingsResult.success || !allBookingsResult.data) {
        return { success: false, error: 'Failed to get bookings' };
      }

      const todaysDepartures = allBookingsResult.data.filter(booking => {
        const checkOutDate = new Date(booking.checkOutDate);
        return checkOutDate >= today && checkOutDate < tomorrow;
      });

      return { success: true, data: todaysDepartures };
    } catch (error) {
      return {
        success: false,
        error: error instanceof DatabaseError ? error.message : 'Failed to get today\'s departures'
      };
    }
  }

  /**
   * Check if a room is available for given dates
   */
  async isRoomAvailable(
    roomId: string,
    checkInDate: Date,
    checkOutDate: Date,
    excludeBookingId?: string
  ): Promise<DatabaseResult<boolean>> {
    try {
      const allBookingsResult = await this.getAllBookings();
      if (!allBookingsResult.success || !allBookingsResult.data) {
        return { success: false, error: 'Failed to check room availability' };
      }

      const conflictingBookings = allBookingsResult.data.filter(booking => {
        if (excludeBookingId && booking.id === excludeBookingId) {
          return false;
        }

        if (booking.room.id !== roomId) {
          return false;
        }

        if (booking.status === BookingStatus.Cancelled) {
          return false;
        }

        const bookingCheckIn = new Date(booking.checkInDate);
        const bookingCheckOut = new Date(booking.checkOutDate);

        // Check for date overlap
        return (
          (checkInDate < bookingCheckOut && checkOutDate > bookingCheckIn)
        );
      });

      return { success: true, data: conflictingBookings.length === 0 };
    } catch (error) {
      return {
        success: false,
        error: error instanceof DatabaseError ? error.message : 'Failed to check room availability'
      };
    }
  }
}

export const bookingService = new BookingService();
