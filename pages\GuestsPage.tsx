
import React, { useState, useEffect } from 'react';
import { Guest, UserRole } from '../types';
import { guestService } from '../services/guestService';
import { useAsyncOperation } from '../hooks/useAsyncOperation';
import { useNotifications } from '../hooks/useNotifications';
import { useConfirmation } from '../components/shared/ConfirmationDialog';
import { useDebounce } from '../hooks/usePerformance';
import Button from '../components/shared/Button';
import { Input } from '../components/shared/AccessibleForm';
import Card from '../components/shared/Card';
import { LoadingOverlay, Skeleton, ErrorState } from '../components/shared/LoadingStates';
import CustomerForm from '../components/customers/CustomerForm';
import CustomerList from '../components/customers/CustomerList';
import CustomerDetails from '../components/customers/CustomerDetails';
import { PlusIcon, SearchIcon } from '../components/shared/Icon';

interface GuestsPageProps {
  userRole: UserRole;
}

const GuestsPage: React.FC<GuestsPageProps> = ({ userRole: _userRole }) => {
  const [guests, setGuests] = useState<Guest[]>([]);
  const [filteredGuests, setFilteredGuests] = useState<Guest[]>([]);
  const [searchTerm, setSearchTerm] = useState('');

  // Modal states
  const [showForm, setShowForm] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [selectedGuest, setSelectedGuest] = useState<Guest | undefined>();

  // Enhanced async operations
  const { state: guestsState, execute: loadGuestsData } = useAsyncOperation<Guest[]>([], {
    retryCount: 3,
    onError: (error) => notifications.error('Failed to load guests', error)
  });

  const { state: deleteState, execute: deleteGuest } = useAsyncOperation(null);

  // Notifications and confirmations
  const notifications = useNotifications();
  const { confirm, ConfirmationComponent } = useConfirmation();

  // Debounced search for better performance
  const debouncedSearch = useDebounce((term: string) => {
    if (term.trim()) {
      performSearch(term);
    } else {
      setFilteredGuests(guests);
    }
  }, 300);

  useEffect(() => {
    const fetchGuests = async () => {
      await loadGuestsData(async () => {
        const result = await guestService.getAllGuests();
        if (!result.success) {
          throw new Error(result.error || 'Failed to load guests');
        }

        const guestData = result.data || [];
        setGuests(guestData);
        setFilteredGuests(guestData);
        return guestData;
      });
    };

    fetchGuests();
  }, [loadGuestsData, notifications]);

  /**
   * Enhanced search functionality with debouncing
   */
  const performSearch = async (term: string) => {
    try {
      const result = await guestService.searchGuests(term);
      if (result.success && result.data) {
        setFilteredGuests(result.data);
      } else {
        notifications.warning('Search failed', result.error || 'Unable to search guests');
        setFilteredGuests(guests); // Fallback to all guests
      }
    } catch (error) {
      notifications.error('Search error', 'An error occurred while searching guests');
      setFilteredGuests(guests);
    }
  };

  /**
   * Handle search input changes with debouncing
   */
  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    debouncedSearch(value);
  };

  const handleAddGuest = () => {
    setSelectedGuest(undefined);
    setShowForm(true);
  };

  const handleEditGuest = (guestId: string) => {
    const guest = guests.find(g => g.id === guestId);
    setSelectedGuest(guest);
    setShowForm(true);
  };

  const handleViewDetails = (guestId: string) => {
    const guest = guests.find(g => g.id === guestId);
    setSelectedGuest(guest);
    setShowDetails(true);
  };

  /**
   * Enhanced delete guest with confirmation and error handling
   */
  const handleDeleteGuest = async (guestId: string) => {
    const guest = guests.find(g => g.id === guestId);
    if (!guest) return;

    const confirmed = await confirm({
      title: 'Delete Guest',
      message: `Are you sure you want to delete ${guest.firstName} ${guest.lastName}? This action cannot be undone.`,
      type: 'delete',
      confirmText: 'Delete Guest',
      dangerous: true
    });

    if (!confirmed) return;

    await deleteGuest(async () => {
      const result = await guestService.deleteGuest(guestId);
      if (!result.success) {
        throw new Error(result.error || 'Failed to delete guest');
      }

      // Refresh data after successful deletion
      const guestsResult = await guestService.getAllGuests();
      if (guestsResult.success && guestsResult.data) {
        setGuests(guestsResult.data);
        setFilteredGuests(guestsResult.data);
        notifications.success('Guest deleted successfully', `${guest.firstName} ${guest.lastName} has been deleted.`);
      }

      return result;
    });
  };

  /**
   * Enhanced save guest handler
   */
  const handleSaveGuest = async (guest: Guest) => {
    setShowForm(false);
    setSelectedGuest(undefined);

    // Refresh the guests list
    const result = await guestService.getAllGuests();
    if (result.success && result.data) {
      setGuests(result.data);
      setFilteredGuests(result.data);
    }
  };

  const handleCancelForm = () => {
    setShowForm(false);
    setSelectedGuest(undefined);
  };

  const handleCloseDetails = () => {
    setShowDetails(false);
    setSelectedGuest(undefined);
  };

  const handleEditFromDetails = () => {
    setShowDetails(false);
    setShowForm(true);
  };

  // Enhanced loading state with skeleton
  if (guestsState.loading) {
    return (
      <div className="space-y-6">
        <Card>
          <div className="flex justify-between items-center mb-6">
            <Skeleton width={200} height={32} />
            <Skeleton width={150} height={40} />
          </div>

          <div className="mb-6">
            <Skeleton width="100%" height={40} />
          </div>

          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, index) => (
              <Skeleton key={index} variant="rectangular" height={80} />
            ))}
          </div>
        </Card>
      </div>
    );
  }

  // Enhanced error state
  if (guestsState.error) {
    return (
      <ErrorState
        title="Failed to Load Guests"
        description="We couldn't load the guest data. Please try again."
        error={guestsState.error}
        onRetry={() => window.location.reload()}
      />
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <div className="flex flex-col md:flex-row justify-between items-center gap-4 mb-6">
          <h2 className="text-2xl font-poppins font-semibold text-navy-dark dark:text-gold-accent">
            Guest Management
          </h2>
          <Button
            onClick={handleAddGuest}
            leftIcon={<PlusIcon className="w-5 h-5"/>}
            variant="primary"
          >
            Add New Guest
          </Button>
        </div>

        <div className="mb-6">
          <Input
            placeholder="Search guests by name, email, or phone..."
            value={searchTerm}
            onChange={(e) => handleSearchChange(e.target.value)}
            leftIcon={<SearchIcon className="w-5 h-5"/>}
          />
        </div>

        <CustomerList
          customers={filteredGuests}
          onViewDetails={handleViewDetails}
          onEdit={handleEditGuest}
          onDelete={handleDeleteGuest}
          loading={loading}
        />
      </Card>

      {/* Customer Form Modal */}
      <CustomerForm
        customer={selectedGuest}
        onSave={handleSaveGuest}
        onCancel={handleCancelForm}
        isOpen={showForm}
      />

      {/* Customer Details Modal */}
      {selectedGuest && (
        <CustomerDetails
          customer={selectedGuest}
          onClose={handleCloseDetails}
          onEdit={handleEditFromDetails}
          isOpen={showDetails}
        />
      )}

      {/* Global Confirmation Dialog */}
      {ConfirmationComponent}
    </div>
  );
};

export default GuestsPage;
