
import React from 'react';
import { HousekeepingTask } from '../../types';
import Badge from '../shared/Badge';
import Button from '../shared/Button';
import { EyeIcon } from '../shared/Icon'; // Placeholder icons

interface TaskItemProps {
  task: HousekeepingTask;
  onViewDetails: (taskId: string) => void;
  onUpdateStatus: (taskId: string, newStatus: 'Pending' | 'In Progress' | 'Completed' | 'Blocked') => void;
}

const TaskItem: React.FC<TaskItemProps> = ({ task, onViewDetails, onUpdateStatus }) => {
  const getStatusColor = (status: HousekeepingTask['status']) => {
    switch (status) {
      case 'Pending': return 'bg-status-yellow text-gray-800';
      case 'In Progress': return 'bg-blue-500 text-white';
      case 'Completed': return 'bg-status-green text-white';
      case 'Blocked': return 'bg-status-red text-white';
      default: return 'bg-gray-200 text-gray-800';
    }
  };

  const getPriorityColor = (priority: HousekeepingTask['priority']) => {
    switch (priority) {
      case 'High': return 'border-status-red';
      case 'Medium': return 'border-status-yellow';
      case 'Low': return 'border-status-green';
      default: return 'border-gray-300';
    }
  };

  return (
    <div className={`p-4 bg-white dark:bg-navy shadow-md rounded-lg border-l-4 ${getPriorityColor(task.priority)} hover:shadow-lg transition-shadow`}>
      <div className="flex justify-between items-start mb-2">
        <h4 className="text-lg font-semibold text-navy-dark dark:text-gold-accent">
          Room {task.roomNumber} - {task.taskType}
        </h4>
        <Badge colorClass={getStatusColor(task.status)}>{task.status}</Badge>
      </div>
      {task.description && <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">{task.description}</p>}
      <p className="text-xs text-gray-500 dark:text-gray-400">Reported: {new Date(task.reportedAt).toLocaleString()}</p>
      {task.assignedTo && <p className="text-xs text-gray-500 dark:text-gray-400">Assigned to: {task.assignedTo}</p>}
      
      <div className="mt-3 flex justify-end space-x-2">
        {task.status !== 'Completed' && (
          <select 
            value={task.status} 
            onChange={(e) => onUpdateStatus(task.id, e.target.value as HousekeepingTask['status'])}
            className="text-xs p-1.5 border border-medium-gray dark:border-navy-light rounded-md bg-white dark:bg-navy text-dark-gray dark:text-light-gray focus:ring-gold-accent focus:border-gold-accent"
          >
            <option value="Pending">Pending</option>
            <option value="In Progress">In Progress</option>
            <option value="Completed">Completed</option>
            <option value="Blocked">Blocked</option>
          </select>
        )}
        <Button variant="ghost" size="sm" onClick={() => onViewDetails(task.id)} title="View Details">
          <EyeIcon className="w-5 h-5 text-blue-500" />
        </Button>
      </div>
    </div>
  );
};

export default TaskItem;
