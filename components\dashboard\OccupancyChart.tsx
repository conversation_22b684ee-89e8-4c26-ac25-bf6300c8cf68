
import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, <PERSON>lt<PERSON>, Legend, ResponsiveContainer } from 'recharts';
import Card from '../shared/Card';
import { useTheme } from '../../hooks/useThemeHook';
import { roomService } from '../../services/roomService';

interface OccupancyData {
  name: string; // Day of week or date
  occupied: number;
  available: number;
}

const OccupancyChart: React.FC = () => {
  const [data, setData] = useState<OccupancyData[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadOccupancyData = async () => {
      try {
        const result = await roomService.getRoomStats();

        // Show current occupancy data only
        // No mock or generated data - only real data from the database
        if (result.success && result.data && result.data.total > 0) {
          const currentData = [{
            name: 'Current',
            occupied: Math.round(result.data.occupancyRate),
            available: Math.round(100 - result.data.occupancyRate)
          }];
          setData(currentData);
        } else {
          // No rooms exist yet
          setData([]);
        }
      } catch (error) {
        console.error('Error loading occupancy data:', error);
        setData([]);
      } finally {
        setLoading(false);
      }
    };

    loadOccupancyData();
  }, []);
  const { theme } = useTheme();
  const tickColor = theme === 'dark' ? '#A0AEC0' : '#4A5568'; // gray-500 or gray-700
  const barColorOccupied = theme === 'dark' ? '#DAA520' : '#2D4A70'; // gold-accent or navy
  const barColorAvailable = theme === 'dark' ? '#4A6A94' : '#A0AEC0'; // navy-light or gray-400


  if (loading) {
    return (
      <Card title="Current Occupancy Rate">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </Card>
    );
  }

  if (data.length === 0) {
    return (
      <Card title="Current Occupancy Rate">
        <div className="flex items-center justify-center h-64">
          <p className="text-muted-foreground">No rooms available yet. Add rooms to see occupancy data.</p>
        </div>
      </Card>
    );
  }

  return (
    <Card title="Current Occupancy Rate">
      <div style={{ width: '100%', height: 300 }}>
        <ResponsiveContainer>
          <BarChart data={data} margin={{ top: 5, right: 20, left: -20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" stroke={theme === 'dark' ? '#4A5563' : '#E2E8F0'} />
            <XAxis dataKey="name" tick={{ fill: tickColor, fontSize: 12 }} />
            <YAxis tick={{ fill: tickColor, fontSize: 12 }} unit="%" />
            <Tooltip
              contentStyle={{ 
                backgroundColor: theme === 'dark' ? '#1A2E4C' : '#FFFFFF', // navy-dark or white
                borderColor: theme === 'dark' ? '#4A6A94' : '#E2E8F0', // navy-light or gray-300
                borderRadius: '0.5rem',
              }}
              labelStyle={{ color: theme === 'dark' ? '#F3F4F6' : '#1F2937' }} // light-gray or gray-800
              itemStyle={{ color: theme === 'dark' ? '#F3F4F6' : '#1F2937' }}
            />
            <Legend wrapperStyle={{ fontSize: '14px', color: tickColor }} />
            <Bar dataKey="occupied" name="Occupied" stackId="a" fill={barColorOccupied} radius={[4, 4, 0, 0]} barSize={20} />
            <Bar dataKey="available" name="Available" stackId="a" fill={barColorAvailable} radius={[4, 4, 0, 0]} barSize={20} />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </Card>
  );
};

export default OccupancyChart;
