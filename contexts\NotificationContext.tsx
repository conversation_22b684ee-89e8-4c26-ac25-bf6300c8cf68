import React, { createContext, useContext, ReactNode } from 'react';
import { useNotifications, NotificationOptions, NotificationType } from '../hooks/useNotifications';

/**
 * Notification context interface
 */
interface NotificationContextType {
  notifications: ReturnType<typeof useNotifications>['notifications'];
  addNotification: ReturnType<typeof useNotifications>['addNotification'];
  removeNotification: ReturnType<typeof useNotifications>['removeNotification'];
  clearAll: ReturnType<typeof useNotifications>['clearAll'];
  clearByType: ReturnType<typeof useNotifications>['clearByType'];
  success: ReturnType<typeof useNotifications>['success'];
  error: ReturnType<typeof useNotifications>['error'];
  warning: ReturnType<typeof useNotifications>['warning'];
  info: ReturnType<typeof useNotifications>['info'];
}

/**
 * Notification context
 */
const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

/**
 * Notification provider props
 */
interface NotificationProviderProps {
  children: ReactNode;
  maxNotifications?: number;
}

/**
 * Notification provider component
 */
export const NotificationProvider: React.FC<NotificationProviderProps> = ({
  children,
  maxNotifications = 5
}) => {
  const notifications = useNotifications(maxNotifications);

  return (
    <NotificationContext.Provider value={notifications}>
      {children}
    </NotificationContext.Provider>
  );
};

/**
 * Hook to use notification context
 */
export const useNotificationContext = (): NotificationContextType => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotificationContext must be used within a NotificationProvider');
  }
  return context;
};

/**
 * Higher-order component for notification context
 */
export const withNotifications = <P extends object>(
  Component: React.ComponentType<P>
): React.ComponentType<P> => {
  return (props: P) => (
    <NotificationProvider>
      <Component {...props} />
    </NotificationProvider>
  );
};

export default NotificationContext;
