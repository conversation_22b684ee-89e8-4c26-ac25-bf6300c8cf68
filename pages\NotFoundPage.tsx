
import React from 'react';
import { Link } from 'react-router-dom';
import Card from '../components/shared/Card';
import Button from '../components/shared/Button';
import { HomeIcon } from '../components/shared/Icon';

const NotFoundPage: React.FC = () => {
  return (
    <div className="flex items-center justify-center min-h-[calc(100vh-10rem)]">
      <Card className="text-center p-8 md:p-12 max-w-lg w-full">
        <h1 className="text-6xl font-poppins font-bold text-gold-accent mb-4">404</h1>
        <h2 className="text-3xl font-poppins font-semibold text-navy-dark dark:text-light-gray mb-6">Page Not Found</h2>
        <p className="text-gray-600 dark:text-gray-300 mb-8">
          Oops! The page you are looking for does not exist. It might have been moved or deleted.
        </p>
        <Button 
          variant="primary" 
          size="lg" 
          leftIcon={<HomeIcon className="w-5 h-5"/>}
          onClick={() => { /* Handled by Link */ }}
        >
          <Link to="/">Go to Dashboard</Link>
        </Button>
      </Card>
    </div>
  );
};

export default NotFoundPage;
