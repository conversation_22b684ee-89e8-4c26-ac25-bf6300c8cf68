
import React, { useState, useEffect, useMemo } from 'react';
import { HousekeepingTask, UserRole } from '../types';
import { housekeepingService } from '../services/housekeepingService';
import TaskItem from '../components/housekeeping/TaskItem';
import Button from '../components/shared/Button';
import Select from '../components/shared/Select';
import Input from '../components/shared/Input';
import Card from '../components/shared/Card';
import { PlusIcon, SearchIcon } from '../components/shared/Icon';

interface HousekeepingPageProps {
  userRole: UserRole;
}

const HousekeepingPage: React.FC<HousekeepingPageProps> = ({ userRole }) => {
  const [tasks, setTasks] = useState<HousekeepingTask[]>([]);
  const [loading, setLoading] = useState(true);

  // Remove unused variable warning
  console.log('Loading state:', loading);
  const [filterStatus, setFilterStatus] = useState<HousekeepingTask['status'] | ''>('');
  const [filterPriority, setFilterPriority] = useState<HousekeepingTask['priority'] | ''>('');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    const loadTasks = async () => {
      try {
        const allTasks = await housekeepingService.getAllTasks();
        setTasks(allTasks);
      } catch (error) {
        console.error('Error loading housekeeping tasks:', error);
      } finally {
        setLoading(false);
      }
    };

    loadTasks();
  }, []);

  const handleViewDetails = (taskId: string) => {
    // TODO: Implement task details modal/page
    alert(`View details for task ${taskId}`);
  };

  const handleUpdateStatus = async (taskId: string, newStatus: HousekeepingTask['status']) => {
    try {
      if (newStatus === 'In Progress') {
        await housekeepingService.startTask(taskId);
      } else if (newStatus === 'Completed') {
        await housekeepingService.completeTask(taskId);
      } else if (newStatus === 'Blocked') {
        await housekeepingService.blockTask(taskId);
      }

      // Reload tasks
      const updatedTasks = await housekeepingService.getAllTasks();
      setTasks(updatedTasks);
    } catch (error) {
      console.error('Error updating task status:', error);
      alert('Failed to update task status');
    }
  };

  const handleCreateTask = () => {
    // TODO: Implement create task modal/page
    alert('Create new housekeeping task form');
  };

  const filteredTasks = useMemo(() => {
    return tasks.filter(task => {
      const matchesSearch = task.roomNumber.includes(searchTerm) || (task.description && task.description.toLowerCase().includes(searchTerm.toLowerCase()));
      const matchesStatus = filterStatus ? task.status === filterStatus : true;
      const matchesPriority = filterPriority ? task.priority === filterPriority : true;
      return matchesSearch && matchesStatus && matchesPriority;
    }).sort((a,b) => { // Sort by priority then reportedAt
        const priorityOrder: Record<string, number> = { 'High': 1, 'Medium': 2, 'Low': 3, 'Critical': 0 };
        if(priorityOrder[a.priority] !== priorityOrder[b.priority]) {
            return priorityOrder[a.priority] - priorityOrder[b.priority];
        }
        return new Date(b.reportedAt).getTime() - new Date(a.reportedAt).getTime();
    });
  }, [tasks, searchTerm, filterStatus, filterPriority]);

  const statusOptions = [
    { value: '', label: 'All Statuses' },
    { value: 'Pending', label: 'Pending' },
    { value: 'In Progress', label: 'In Progress' },
    { value: 'Completed', label: 'Completed' },
    { value: 'Blocked', label: 'Blocked' },
  ];
  const priorityOptions = [
    { value: '', label: 'All Priorities' },
    { value: 'High', label: 'High' },
    { value: 'Medium', label: 'Medium' },
    { value: 'Low', label: 'Low' },
  ];

  return (
    <div className="space-y-6">
      <Card>
        <div className="flex flex-col md:flex-row justify-between items-center gap-4 mb-6">
          <h2 className="text-2xl font-poppins font-semibold text-navy-dark dark:text-gold-accent">Housekeeping Tasks</h2>
          {(userRole === UserRole.Admin || userRole === UserRole.Manager || userRole === UserRole.Housekeeping) && (
             <Button onClick={handleCreateTask} leftIcon={<PlusIcon className="w-5 h-5"/>} variant="primary">
                Create Task
             </Button>
          )}
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6 p-4 bg-light-gray dark:bg-navy-light rounded-lg">
            <Input
                placeholder="Search by room or description..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                icon={<SearchIcon className="w-4 h-4" />}
            />
            <Select
                options={statusOptions}
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value as HousekeepingTask['status'] | '')}
            />
            <Select
                options={priorityOptions}
                value={filterPriority}
                onChange={(e) => setFilterPriority(e.target.value as HousekeepingTask['priority'] | '')}
            />
        </div>
      </Card>

      {filteredTasks.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTasks.map(task => (
            <TaskItem 
              key={task.id} 
              task={task} 
              onViewDetails={handleViewDetails} 
              onUpdateStatus={handleUpdateStatus} 
            />
          ))}
        </div>
      ) : (
        <Card>
            <p className="text-center py-12 text-gray-500 dark:text-gray-400">No housekeeping tasks found.</p>
        </Card>
      )}
    </div>
  );
};

export default HousekeepingPage;
