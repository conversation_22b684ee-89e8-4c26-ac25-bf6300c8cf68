
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { fileURLToPath, URL } from 'node:url';

export default defineConfig({
      plugins: [react()],
      resolve: {
        alias: {
          '@': fileURLToPath(new URL('.', import.meta.url)),
        }
      },
      css: {
        postcss: './postcss.config.js',
      },
      server: {
        port: 3000,
        open: true,
      },
      build: {
        target: 'esnext',
        minify: 'esbuild',
        sourcemap: false,
      },
});
