/**
 * Integration tests for the enhanced hotel management system
 * This file tests the integration of all new improvements
 */

import { roomService } from '../services/roomService';
import { guestService } from '../services/guestService';
import { bookingService } from '../services/bookingService';
import { Room, Guest, Booking, RoomStatus, BookingStatus } from '../types';

/**
 * Mock data for testing
 */
const mockRoomType = {
  id: 'rt-1',
  name: 'Standard Room',
  basePrice: 100,
  amenities: ['WiFi', 'TV', 'Air Conditioning'] as const,
  capacity: 2,
  createdAt: new Date(),
  updatedAt: new Date()
};

const mockRoom: Room = {
  id: 'room-1',
  roomNumber: '101',
  type: mockRoomType,
  status: RoomStatus.Available,
  floor: 1,
  currentBookingId: null,
  createdAt: new Date(),
  updatedAt: new Date()
};

const mockGuest: Guest = {
  id: 'guest-1',
  firstName: '<PERSON>',
  lastName: 'Doe',
  email: '<EMAIL>',
  phone: '+1234567890',
  address: '123 Main St, City, State',
  preferences: ['Non-smoking', 'High floor'] as const,
  pastBookings: [] as const,
  createdAt: new Date(),
  updatedAt: new Date()
};

/**
 * Test enhanced room service operations
 */
export async function testRoomServiceIntegration(): Promise<boolean> {
  try {
    console.log('Testing Room Service Integration...');

    // Test adding a room
    const addResult = await roomService.addRoom(mockRoom);
    if (!addResult.success) {
      console.error('Failed to add room:', addResult.error);
      return false;
    }
    console.log('✓ Room added successfully');

    // Test getting room by number
    const getByNumberResult = await roomService.getRoomByNumber('101');
    if (!getByNumberResult.success || !getByNumberResult.data) {
      console.error('Failed to get room by number:', getByNumberResult.error);
      return false;
    }
    console.log('✓ Room retrieved by number successfully');

    // Test updating room status
    const updateStatusResult = await roomService.updateRoomStatus(
      mockRoom.id,
      RoomStatus.Cleaning
    );
    if (!updateStatusResult.success) {
      console.error('Failed to update room status:', updateStatusResult.error);
      return false;
    }
    console.log('✓ Room status updated successfully');

    // Test getting room statistics
    const statsResult = await roomService.getRoomStats();
    if (!statsResult.success || !statsResult.data) {
      console.error('Failed to get room statistics:', statsResult.error);
      return false;
    }
    console.log('✓ Room statistics retrieved successfully');

    // Test deleting room
    const deleteResult = await roomService.deleteRoom(mockRoom.id);
    if (!deleteResult.success) {
      console.error('Failed to delete room:', deleteResult.error);
      return false;
    }
    console.log('✓ Room deleted successfully');

    return true;
  } catch (error) {
    console.error('Room service integration test failed:', error);
    return false;
  }
}

/**
 * Test enhanced guest service operations
 */
export async function testGuestServiceIntegration(): Promise<boolean> {
  try {
    console.log('Testing Guest Service Integration...');

    // Test adding a guest
    const addResult = await guestService.addGuest(mockGuest);
    if (!addResult.success) {
      console.error('Failed to add guest:', addResult.error);
      return false;
    }
    console.log('✓ Guest added successfully');

    // Test getting guest by email
    const getByEmailResult = await guestService.getGuestByEmail('<EMAIL>');
    if (!getByEmailResult.success || !getByEmailResult.data) {
      console.error('Failed to get guest by email:', getByEmailResult.error);
      return false;
    }
    console.log('✓ Guest retrieved by email successfully');

    // Test searching guests
    const searchResult = await guestService.searchGuests('John');
    if (!searchResult.success || !searchResult.data) {
      console.error('Failed to search guests:', searchResult.error);
      return false;
    }
    console.log('✓ Guest search completed successfully');

    // Test updating guest
    const updatedGuest = { ...mockGuest, phone: '+0987654321' };
    const updateResult = await guestService.updateGuest(updatedGuest);
    if (!updateResult.success) {
      console.error('Failed to update guest:', updateResult.error);
      return false;
    }
    console.log('✓ Guest updated successfully');

    // Test deleting guest
    const deleteResult = await guestService.deleteGuest(mockGuest.id);
    if (!deleteResult.success) {
      console.error('Failed to delete guest:', deleteResult.error);
      return false;
    }
    console.log('✓ Guest deleted successfully');

    return true;
  } catch (error) {
    console.error('Guest service integration test failed:', error);
    return false;
  }
}

/**
 * Test enhanced booking service operations
 */
export async function testBookingServiceIntegration(): Promise<boolean> {
  try {
    console.log('Testing Booking Service Integration...');

    // First, add required room and guest
    const roomResult = await roomService.addRoom(mockRoom);
    const guestResult = await guestService.addGuest(mockGuest);

    if (!roomResult.success || !guestResult.success) {
      console.error('Failed to setup test data for booking');
      return false;
    }

    const mockBooking: Booking = {
      id: 'booking-1',
      guest: mockGuest,
      room: mockRoom,
      checkInDate: new Date(),
      checkOutDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
      numberOfGuests: 2,
      totalAmount: 100,
      status: BookingStatus.Confirmed,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Test adding a booking
    const addResult = await bookingService.addBooking(mockBooking);
    if (!addResult.success) {
      console.error('Failed to add booking:', addResult.error);
      return false;
    }
    console.log('✓ Booking added successfully');

    // Test checking room availability
    const availabilityResult = await bookingService.isRoomAvailable(
      mockRoom.id,
      new Date(),
      new Date(Date.now() + 24 * 60 * 60 * 1000)
    );
    if (!availabilityResult.success) {
      console.error('Failed to check room availability:', availabilityResult.error);
      return false;
    }
    console.log('✓ Room availability checked successfully');

    // Test getting today's arrivals
    const arrivalsResult = await bookingService.getTodaysArrivals();
    if (!arrivalsResult.success) {
      console.error('Failed to get today\'s arrivals:', arrivalsResult.error);
      return false;
    }
    console.log('✓ Today\'s arrivals retrieved successfully');

    // Cleanup
    await bookingService.deleteBooking(mockBooking.id);
    await roomService.deleteRoom(mockRoom.id);
    await guestService.deleteGuest(mockGuest.id);

    return true;
  } catch (error) {
    console.error('Booking service integration test failed:', error);
    return false;
  }
}

/**
 * Run all integration tests
 */
export async function runAllIntegrationTests(): Promise<void> {
  console.log('🧪 Starting Enhanced Hotel Management System Integration Tests...\n');

  const results = await Promise.all([
    testRoomServiceIntegration(),
    testGuestServiceIntegration(),
    testBookingServiceIntegration()
  ]);

  const allPassed = results.every(result => result);

  console.log('\n📊 Integration Test Results:');
  console.log(`Room Service: ${results[0] ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`Guest Service: ${results[1] ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`Booking Service: ${results[2] ? '✅ PASSED' : '❌ FAILED'}`);

  if (allPassed) {
    console.log('\n🎉 All integration tests passed! The enhanced system is working correctly.');
  } else {
    console.log('\n⚠️ Some integration tests failed. Please check the errors above.');
  }
}

// Export for use in development
if (typeof window !== 'undefined') {
  (window as any).runIntegrationTests = runAllIntegrationTests;
}
