
import React from 'react';

interface SelectOption {
  value: string | number;
  label: string;
}

interface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  label?: string;
  options: SelectOption[];
  error?: string | undefined;
  icon?: React.ReactNode;
  placeholder?: string; // Added to support custom placeholder text for the first disabled option
}

const Select: React.FC<SelectProps> = ({ label, id, options, error, icon, className = '', placeholder, ...props }) => {
  const baseSelectClasses = "block w-full pl-3 pr-10 py-2.5 border rounded-lg shadow-sm focus:outline-none focus:ring-2 sm:text-sm appearance-none transition-colors duration-150";
  const normalClasses = "border-medium-gray dark:border-navy-light bg-white dark:bg-navy focus:ring-gold-accent dark:focus:ring-gold-accent text-dark-gray dark:text-light-gray";
  const errorClasses = "border-status-red dark:border-red-500 focus:ring-status-red dark:focus:ring-red-500";

  return (
    <div className="w-full">
      {label && <label htmlFor={id} className="block text-sm font-medium text-dark-gray dark:text-light-gray mb-1">{label}</label>}
      <div className="relative">
        {icon && <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-gray-400 dark:text-gray-500">{icon}</div>}
        <select
          id={id}
          className={`${baseSelectClasses} ${error ? errorClasses : normalClasses} ${icon ? 'pl-10' : ''} ${className}`}
          {...props}
        >
          {placeholder && <option value="" disabled>{placeholder}</option>}
          {options.map(option => (
            <option key={option.value} value={option.value}>{option.label}</option>
          ))}
        </select>
        <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
          <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
            <path d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
          </svg>
        </div>
      </div>
      {error && <p className="mt-1 text-xs text-status-red dark:text-red-400">{error}</p>}
    </div>
  );
};

export default Select;