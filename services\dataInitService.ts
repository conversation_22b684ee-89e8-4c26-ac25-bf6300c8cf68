import { initDatabase } from '../lib/database';
import { roomTypeService } from './roomTypeService';

export class DataInitService {
  private static initialized = false;

  static async initializeApp(): Promise<void> {
    if (this.initialized) return;

    try {
      // Initialize IndexedDB
      await initDatabase();

      // Initialize default room types if none exist
      await roomTypeService.initializeDefaultRoomTypes();

      console.log('Database initialized successfully');
      this.initialized = true;
    } catch (error) {
      console.error('Failed to initialize database:', error);
      throw error;
    }
  }



  static async clearAllData(): Promise<void> {
    try {
      // This would clear all data from the database
      // Implement if needed for development/testing
      console.log('All data cleared');
    } catch (error) {
      console.error('Failed to clear data:', error);
      throw error;
    }
  }

  static isInitialized(): boolean {
    return this.initialized;
  }
}

export const dataInitService = DataInitService;
