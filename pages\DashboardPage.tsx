
import React, { useState, useEffect } from 'react';
import SummaryCard from '../components/dashboard/SummaryCard';
import OccupancyChart from '../components/dashboard/OccupancyChart';
import ActivityFeed from '../components/dashboard/ActivityFeed';
import { UserRole } from '../types';
import { roomService } from '../services/roomService';
import { bookingService } from '../services/bookingService';
import { useAsyncOperation } from '../hooks/useAsyncOperation';
import { useNotifications } from '../hooks/useNotifications';
import { LoadingOverlay, Skeleton } from '../components/shared/LoadingStates';
import { KeyIcon, CalendarIcon, ClipboardListIcon } from '../components/shared/Icon';

interface DashboardPageProps {
  userRole: UserRole;
}

const DashboardPage: React.FC<DashboardPageProps> = ({ userRole }) => {
  const [stats, setStats] = useState({
    totalRooms: 0,
    occupiedRooms: 0,
    availableRooms: 0,
    roomsToClean: 0,
    arrivalsToday: 0,
    departuresToday: 0
  });

  // Enhanced async operations management
  const { state: dashboardState, execute: loadDashboardData } = useAsyncOperation(null, {
    retryCount: 3,
    onError: (error) => notifications.error('Failed to load dashboard data', error)
  });

  const notifications = useNotifications();

  useEffect(() => {
    const fetchDashboardData = async () => {
      await loadDashboardData(async () => {
        // Load room statistics with enhanced error handling
        const roomStatsResult = await roomService.getRoomStats();
        if (!roomStatsResult.success) {
          throw new Error(roomStatsResult.error || 'Failed to load room statistics');
        }

        // Load booking statistics for today
        const todaysArrivalsResult = await bookingService.getTodaysArrivals();
        const todaysDeparturesResult = await bookingService.getTodaysDepartures();

        const arrivals = todaysArrivalsResult.success ? todaysArrivalsResult.data || [] : [];
        const departures = todaysDeparturesResult.success ? todaysDeparturesResult.data || [] : [];

        const newStats = {
          totalRooms: roomStatsResult.data!.total,
          occupiedRooms: roomStatsResult.data!.occupied,
          availableRooms: roomStatsResult.data!.available,
          roomsToClean: roomStatsResult.data!.cleaning,
          arrivalsToday: arrivals.length,
          departuresToday: departures.length
        };

        setStats(newStats);
        return newStats;
      });
    };

    fetchDashboardData();
  }, [loadDashboardData, notifications]);

  // Enhanced loading state with skeleton
  if (dashboardState.loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {Array.from({ length: 4 }).map((_, index) => (
            <Skeleton key={index} variant="rectangular" height={120} />
          ))}
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <Skeleton variant="rectangular" height={300} />
          </div>
          <div>
            <Skeleton variant="rectangular" height={300} />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <SummaryCard 
          title="Occupied Rooms" 
          value={`${stats.occupiedRooms} / ${stats.totalRooms}`} 
          icon={<KeyIcon />}
          trend={`${((stats.occupiedRooms / stats.totalRooms) * 100).toFixed(0)}%`}
          bgColorClass="bg-red-100 dark:bg-red-700"
          textColorClass="text-status-red dark:text-red-200"
        />
        <SummaryCard 
          title="Available Rooms" 
          value={stats.availableRooms} 
          icon={<KeyIcon />}
          bgColorClass="bg-green-100 dark:bg-green-700"
          textColorClass="text-status-green dark:text-green-200"
        />
        <SummaryCard 
          title="Arrivals Today" 
          value={stats.arrivalsToday} 
          icon={<CalendarIcon />}
          bgColorClass="bg-blue-100 dark:bg-blue-700"
          textColorClass="text-blue-500 dark:text-blue-200"
        />
        <SummaryCard 
          title="Departures Today" 
          value={stats.departuresToday} 
          icon={<CalendarIcon />}
          bgColorClass="bg-purple-100 dark:bg-purple-700"
          textColorClass="text-purple-500 dark:text-purple-200"
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <OccupancyChart />
        </div>
        <div>
          <ActivityFeed />
        </div>
      </div>

      {userRole === UserRole.Housekeeping && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <SummaryCard 
                title="Rooms to Clean" 
                value={stats.roomsToClean} 
                icon={<ClipboardListIcon />}
                bgColorClass="bg-yellow-100 dark:bg-yellow-700"
                textColorClass="text-status-yellow dark:text-yellow-200"
            />
            {/* Add more housekeeping specific cards if needed */}
        </div>
      )}
    </div>
  );
};

export default DashboardPage;
