
import React, { useState, useEffect } from 'react';
import { Routes, Route, useLocation } from 'react-router-dom';
import Sidebar from './components/layout/Sidebar';
import Header from './components/layout/Header';
import MainContent from './components/layout/MainContent';
import DashboardPage from './pages/DashboardPage';
import BookingsPage from './pages/BookingsPage';
import RoomsPage from './pages/RoomsPage';
import HousekeepingPage from './pages/HousekeepingPage';
import GuestsPage from './pages/GuestsPage';
import ReportsPage from './pages/ReportsPage';
import SettingsPage from './pages/SettingsPage';
import NotFoundPage from './pages/NotFoundPage';
import { UserRole } from './types';
import { useTheme } from './hooks/useThemeHook';
import { useAsyncOperation } from './hooks/useAsyncOperation';
import { useNotifications } from './hooks/useNotifications';
import { useErrorHandler } from './hooks/useNotifications';
import { LoadingOverlay, ProgressiveLoading, ErrorState } from './components/shared/LoadingStates';
import { NotificationContainer } from './components/shared/NotificationContainer';
import { dataInitService } from './services/dataInitService';

/**
 * Main application component with enhanced error handling and loading states
 */
const App: React.FC = () => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [currentUserRole, setCurrentUserRole] = useState<UserRole>(UserRole.Admin);
  const { theme } = useTheme();
  const location = useLocation();

  // Enhanced async operation management
  const { state: initState, execute: initializeApp } = useAsyncOperation(null, {
    retryCount: 3,
    retryDelay: 2000,
    timeout: 30000
  });

  // Global notifications and error handling
  const notifications = useNotifications();
  const { handleAsyncError } = useErrorHandler();

  // Initialize database on app start with enhanced error handling
  useEffect(() => {
    const initApp = async () => {
      await initializeApp(async () => {
        const steps = [
          'Initializing database...',
          'Setting up stores...',
          'Loading initial data...',
          'Finalizing setup...'
        ];

        // Simulate progressive loading for better UX
        for (let i = 0; i < steps.length; i++) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }

        await dataInitService.initializeApp();
        return true;
      });
    };

    initApp().catch((error) => {
      handleAsyncError(
        error,
        'Application Initialization',
        { persistent: true }
      );
    });
  }, [initializeApp, handleAsyncError]);

  useEffect(() => {
    document.documentElement.classList.toggle('dark', theme === 'dark');
  }, [theme]);

  // Close sidebar on small screens when navigating
  useEffect(() => {
    if (window.innerWidth < 768) {
      setIsSidebarOpen(false);
    }
  }, [location]);

  const toggleSidebar = () => setIsSidebarOpen(!isSidebarOpen);

  // Enhanced loading state with progressive loading
  if (initState.loading) {
    const loadingSteps = [
      'Initializing database...',
      'Setting up stores...',
      'Loading initial data...',
      'Finalizing setup...'
    ];

    return (
      <div className="flex items-center justify-center h-screen bg-light-gray dark:bg-navy-dark">
        <ProgressiveLoading
          steps={loadingSteps}
          currentStep={Math.floor(Math.random() * loadingSteps.length)} // Simulate progress
          className="max-w-md"
        />
      </div>
    );
  }

  // Enhanced error state with retry functionality
  if (initState.error) {
    return (
      <div className="flex items-center justify-center h-screen bg-light-gray dark:bg-navy-dark">
        <ErrorState
          title="Application Initialization Failed"
          description="We encountered an error while starting the application. Please try again."
          error={initState.error}
          onRetry={() => window.location.reload()}
          className="max-w-md"
        />
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-light-gray dark:bg-navy-dark text-dark-gray dark:text-light-gray overflow-hidden">
      <Sidebar isOpen={isSidebarOpen} toggleSidebar={toggleSidebar} userRole={currentUserRole} />
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header
          toggleSidebar={toggleSidebar}
          isSidebarOpen={isSidebarOpen}
          userRole={currentUserRole}
          setUserRole={setCurrentUserRole}
        />
        <MainContent>
          <Routes>
            <Route path="/" element={<DashboardPage userRole={currentUserRole} />} />
            <Route path="/bookings" element={<BookingsPage userRole={currentUserRole} />} />
            <Route path="/rooms" element={<RoomsPage userRole={currentUserRole} />} />
            <Route path="/housekeeping" element={<HousekeepingPage userRole={currentUserRole} />} />
            <Route path="/guests" element={<GuestsPage userRole={currentUserRole} />} />
            <Route path="/reports" element={<ReportsPage userRole={currentUserRole} />} />
            <Route path="/settings" element={<SettingsPage userRole={currentUserRole} />} />
            <Route path="*" element={<NotFoundPage />} />
          </Routes>
        </MainContent>
      </div>

      {/* Global Notifications */}
      <NotificationContainer
        notifications={notifications.notifications}
        onRemove={notifications.removeNotification}
        onPause={notifications.pauseNotification}
        onResume={notifications.resumeNotification}
      />
    </div>
  );
};

export default App;
