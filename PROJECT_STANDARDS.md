# معايير وقواعد مشروع إدارة الفنادق (Hotel Management Project Standards)

## نظرة عامة (Overview)
هذا المستند يحدد المعايير والقواعد الأساسية لمشروع إدارة الفنادق Premium Stay Hotel Management. يجب على جميع المطورين اتباع هذه المعايير لضمان جودة الكود وسهولة الصيانة.

## 🎯 متطلبات المشروع الأساسية (Core Project Requirements)

### قاعدة البيانات
- **استخدام IndexedDB فقط**: المشروع مصمم للعمل بشكل كامل offline بدون أي dependencies خارجية
- **لا توجد بيانات وهمية**: يجب حذف جميع البيانات الوهمية من المشروع نهائياً
- **التخزين المحلي**: جميع البيانات يجب أن تُحفظ محلياً في المتصفح

### الهدف من المشروع
- مشروع portfolio showcase
- يعمل بشكل كامل offline
- لا يحتاج إلى server أو API خارجي

## 🏗️ هيكلية المشروع (Project Structure)

```
├── components/          # مكونات UI قابلة لإعادة الاستخدام
│   ├── layout/         # مكونات التخطيط (Header, Sidebar)
│   ├── shared/         # مكونات مشتركة (Button, Card, Input)
│   ├── dashboard/      # مكونات خاصة بلوحة التحكم
│   ├── bookings/       # مكونات إدارة الحجوزات
│   ├── rooms/          # مكونات إدارة الغرف
│   └── housekeeping/   # مكونات إدارة التنظيف
├── pages/              # صفحات التطبيق
├── services/           # منطق العمل وخدمات البيانات
├── lib/                # وظائف مساعدة وإعداد قاعدة البيانات
├── hooks/              # React hooks مخصصة
├── src/styles/         # أنماط عامة و Tailwind CSS
├── types.ts            # تعريفات TypeScript
├── constants.ts        # ثوابت التطبيق
└── App.tsx            # المكون الرئيسي
```

## 🛠️ التقنيات المستخدمة (Tech Stack)

### Frontend Framework
- **React 18.3.1** مع **TypeScript 5.7.2**
- **React Router DOM 7.6.2** للتنقل
- **Vite 6.2.0** كأداة البناء

### UI & Styling
- **Tailwind CSS 3.4.17** للتنسيق
- **Radix UI** للمكونات الأساسية
- **Lucide React** للأيقونات
- **Recharts 2.12.7** للرسوم البيانية

### Form Handling & Validation
- **React Hook Form** لإدارة النماذج
- **Zod** للتحقق من صحة البيانات

### Database
- **IndexedDB** للتخزين المحلي

### Code Quality
- **ESLint** للتحقق من جودة الكود
- **Prettier** لتنسيق الكود
- **TypeScript** للتحقق من الأنواع

## 📝 معايير الكود (Coding Standards)

### TypeScript
- استخدم TypeScript في جميع الملفات
- عرّف interfaces واضحة لجميع البيانات
- استخدم enums للقيم الثابتة
- تجنب استخدام `any` type

```typescript
// ✅ صحيح
interface Guest {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
}

// ❌ خطأ
const guest: any = { ... };
```

### React Components
- استخدم Functional Components مع hooks
- اتبع نمط PascalCase لأسماء المكونات
- استخدم TypeScript interfaces للـ props

```typescript
// ✅ صحيح
interface ButtonProps {
  variant?: 'primary' | 'secondary';
  onClick: () => void;
  children: React.ReactNode;
}

const Button: React.FC<ButtonProps> = ({ variant = 'primary', onClick, children }) => {
  return <button className={`btn-${variant}`} onClick={onClick}>{children}</button>;
};
```

### File Naming
- **Components**: PascalCase (e.g., `UserProfile.tsx`)
- **Hooks**: camelCase with "use" prefix (e.g., `useTheme.tsx`)
- **Services**: camelCase (e.g., `roomService.ts`)
- **Types**: camelCase (e.g., `types.ts`)

### Import/Export
- استخدم named exports للمكونات
- رتب imports بالترتيب التالي:
  1. React imports
  2. Third-party libraries
  3. Internal components/hooks
  4. Types
  5. Relative imports

```typescript
// ✅ صحيح
import React from 'react';
import { useNavigate } from 'react-router-dom';
import Button from '../shared/Button';
import { Guest } from '../../types';
import './styles.css';
```

## 🎨 معايير التصميم (Design Standards)

### Color Palette
```css
/* Primary Colors */
--navy: #1A2E4C;           /* الأزرق الداكن الأساسي */
--navy-light: #2D4A70;     /* الأزرق الفاتح */
--navy-dark: #0F1A2A;      /* الأزرق الداكن جداً */
--gold-accent: #DAA520;    /* الذهبي للتمييز */

/* Neutral Colors */
--light-gray: #F3F4F6;     /* خلفية فاتحة */
--medium-gray: #D1D5DB;    /* حدود وفواصل */
--dark-gray: #4B5563;      /* نص في الوضع الفاتح */

/* Status Colors */
--status-green: #10B981;   /* نجاح، متاح */
--status-red: #EF4444;     /* خطأ، مشغول */
--status-yellow: #F59E0B;  /* تحذير، تنظيف */
--status-blue: #3B82F6;    /* معلومات */
```

### Tailwind CSS Classes
- استخدم Tailwind classes بدلاً من CSS مخصص
- اتبع نظام الألوان المحدد
- استخدم responsive design classes

```tsx
// ✅ صحيح
<div className="bg-light-gray dark:bg-navy p-4 rounded-lg shadow-md">
  <h2 className="text-dark-gray dark:text-light-gray text-xl font-semibold">
    Title
  </h2>
</div>
```

### Dark Mode Support
- جميع المكونات يجب أن تدعم الوضع الداكن
- استخدم `dark:` prefix في Tailwind
- اختبر المكونات في كلا الوضعين

## 🗃️ إدارة البيانات (Data Management)

### IndexedDB Structure
```typescript
const STORES = {
  ROOM_TYPES: 'roomTypes',
  ROOMS: 'rooms',
  GUESTS: 'guests',
  BOOKINGS: 'bookings',
  HOUSEKEEPING_TASKS: 'housekeepingTasks',
  STAFF: 'staff',
  SETTINGS: 'settings'
} as const;
```

### Service Layer Pattern
- استخدم service files لمنطق البيانات
- فصل منطق العمل عن UI components
- استخدم async/await للعمليات غير المتزامنة

```typescript
// roomService.ts
export class RoomService {
  static async getAllRooms(): Promise<Room[]> {
    return await db.getAll(STORES.ROOMS);
  }
  
  static async updateRoomStatus(roomId: string, status: RoomStatus): Promise<void> {
    const room = await db.get(STORES.ROOMS, roomId);
    if (room) {
      room.status = status;
      await db.update(STORES.ROOMS, room);
    }
  }
}
```

## 🧪 معايير الاختبار (Testing Standards)

### Unit Testing
- اكتب tests للمكونات المهمة
- اختبر business logic في services
- استخدم meaningful test descriptions

### Manual Testing
- اختبر جميع المكونات في كلا الوضعين (فاتح/داكن)
- تأكد من responsive design
- اختبر offline functionality

## 📦 إدارة التبعيات (Dependency Management)

### Package Management
- استخدم npm للتبعيات
- لا تضف تبعيات غير ضرورية
- حافظ على package.json نظيف

### Version Control
- استخدم semantic versioning
- اكتب commit messages واضحة
- استخدم feature branches

## 🔧 أدوات التطوير (Development Tools)

### ESLint Configuration
```javascript
// eslint.config.js
rules: {
  '@typescript-eslint/no-unused-vars': ['error', { argsIgnorePattern: '^_' }],
  'react/prop-types': 'off',
  'react-refresh/only-export-components': ['warn', { allowConstantExport: true }],
}
```

### Prettier Configuration
```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false
}
```

## 🚀 معايير الأداء (Performance Standards)

### Code Splitting
- استخدم lazy loading للصفحات
- قسم المكونات الكبيرة
- تجنب re-renders غير الضرورية

### Bundle Size
- راقب حجم bundle
- استخدم tree shaking
- تجنب التبعيات الثقيلة

## 📋 قائمة المراجعة (Checklist)

قبل إضافة أي feature جديدة، تأكد من:

- [ ] البحث عن مكونات مشابهة موجودة
- [ ] اتباع نمط التسمية المحدد
- [ ] استخدام TypeScript interfaces
- [ ] دعم الوضع الداكن
- [ ] اتباع نظام الألوان
- [ ] كتابة كود قابل لإعادة الاستخدام
- [ ] اختبار المكون في كلا الوضعين
- [ ] التأكد من responsive design
- [ ] عدم إضافة بيانات وهمية
- [ ] استخدام IndexedDB للتخزين

## 🔄 عملية التطوير (Development Process)

1. **التحليل**: فهم المتطلبات وتحليل الكود الموجود
2. **التصميم**: تخطيط المكونات والبيانات المطلوبة
3. **التطوير**: كتابة الكود وفقاً للمعايير
4. **الاختبار**: اختبار المكونات والوظائف
5. **المراجعة**: مراجعة الكود والتأكد من المعايير
6. **التوثيق**: تحديث التوثيق إذا لزم الأمر

---

**ملاحظة**: هذا المستند يجب أن يُحدث بانتظام مع تطور المشروع وإضافة معايير جديدة.
