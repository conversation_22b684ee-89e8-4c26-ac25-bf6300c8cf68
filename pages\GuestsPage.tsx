
import React, { useState, useEffect } from 'react';
import { Guest, UserRole } from '../types';
import { guestService } from '../services/guestService';
import Button from '../components/shared/Button';
import Input from '../components/shared/Input';
import Card from '../components/shared/Card';
import CustomerForm from '../components/customers/CustomerForm';
import CustomerList from '../components/customers/CustomerList';
import CustomerDetails from '../components/customers/CustomerDetails';
import { PlusIcon, SearchIcon } from '../components/shared/Icon';

interface GuestsPageProps {
  userRole: UserRole;
}

const GuestsPage: React.FC<GuestsPageProps> = ({ userRole: _userRole }) => {
  const [guests, setGuests] = useState<Guest[]>([]);
  const [filteredGuests, setFilteredGuests] = useState<Guest[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  // Modal states
  const [showForm, setShowForm] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [selectedGuest, setSelectedGuest] = useState<Guest | undefined>();

  useEffect(() => {
    loadGuests();
  }, []);

  useEffect(() => {
    filterGuests();
  }, [guests, searchTerm]);

  const loadGuests = async () => {
    try {
      setLoading(true);
      const guestsData = await guestService.getAllGuests();
      setGuests(guestsData);
    } catch (error) {
      console.error('Error loading guests:', error);
    } finally {
      setLoading(false);
    }
  };

  const filterGuests = () => {
    if (!searchTerm.trim()) {
      setFilteredGuests(guests);
      return;
    }

    const filtered = guests.filter(guest =>
      guest.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      guest.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      guest.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      guest.phone.includes(searchTerm)
    );
    setFilteredGuests(filtered);
  };

  const handleAddGuest = () => {
    setSelectedGuest(undefined);
    setShowForm(true);
  };

  const handleEditGuest = (guestId: string) => {
    const guest = guests.find(g => g.id === guestId);
    setSelectedGuest(guest);
    setShowForm(true);
  };

  const handleViewDetails = (guestId: string) => {
    const guest = guests.find(g => g.id === guestId);
    setSelectedGuest(guest);
    setShowDetails(true);
  };

  const handleDeleteGuest = async (guestId: string) => {
    const guest = guests.find(g => g.id === guestId);
    if (!guest) return;

    if (window.confirm(`Are you sure you want to delete ${guest.firstName} ${guest.lastName}?`)) {
      try {
        await guestService.deleteGuest(guestId);
        await loadGuests();
      } catch (error) {
        console.error('Error deleting guest:', error);
        alert('Failed to delete guest. Please try again.');
      }
    }
  };

  const handleSaveGuest = async () => {
    setShowForm(false);
    setSelectedGuest(undefined);
    await loadGuests();
  };

  const handleCancelForm = () => {
    setShowForm(false);
    setSelectedGuest(undefined);
  };

  const handleCloseDetails = () => {
    setShowDetails(false);
    setSelectedGuest(undefined);
  };

  const handleEditFromDetails = () => {
    setShowDetails(false);
    setShowForm(true);
  };

  return (
    <div className="space-y-6">
      <Card>
        <div className="flex flex-col md:flex-row justify-between items-center gap-4 mb-6">
          <h2 className="text-2xl font-poppins font-semibold text-navy-dark dark:text-gold-accent">
            Guest Management
          </h2>
          <Button
            onClick={handleAddGuest}
            leftIcon={<PlusIcon className="w-5 h-5"/>}
            variant="primary"
          >
            Add New Guest
          </Button>
        </div>

        <div className="mb-6">
          <Input
            placeholder="Search guests by name, email, or phone..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            icon={<SearchIcon className="w-5 h-5"/>}
          />
        </div>

        <CustomerList
          customers={filteredGuests}
          onViewDetails={handleViewDetails}
          onEdit={handleEditGuest}
          onDelete={handleDeleteGuest}
          loading={loading}
        />
      </Card>

      {/* Customer Form Modal */}
      <CustomerForm
        customer={selectedGuest}
        onSave={handleSaveGuest}
        onCancel={handleCancelForm}
        isOpen={showForm}
      />

      {/* Customer Details Modal */}
      {selectedGuest && (
        <CustomerDetails
          customer={selectedGuest}
          onClose={handleCloseDetails}
          onEdit={handleEditFromDetails}
          isOpen={showDetails}
        />
      )}
    </div>
  );
};

export default GuestsPage;
