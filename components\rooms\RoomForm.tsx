import React, { useState, useEffect } from 'react';
import { Room, RoomType, RoomStatus, RoomFormData, RoomFormErrors } from '../../types';
import { roomService } from '../../services/roomService';
import { roomTypeService } from '../../services/roomTypeService';
import Button from '../shared/Button';
import Input from '../shared/Input';
import Select from '../shared/Select';
import Card from '../shared/Card';
import { SaveIcon, XIcon } from '../shared/Icon';

interface RoomFormProps {
  room?: Room | undefined;
  onSave: (room: Room) => void;
  onCancel: () => void;
  isOpen: boolean;
}

const RoomForm: React.FC<RoomFormProps> = ({
  room,
  onSave,
  onCancel,
  isOpen
}) => {
  const [formData, setFormData] = useState<RoomFormData>({
    roomNumber: '',
    typeId: '',
    floor: 1,
    notes: ''
  });
  const [roomTypes, setRoomTypes] = useState<RoomType[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingTypes, setLoadingTypes] = useState(true);
  const [errors, setErrors] = useState<RoomFormErrors>({});

  useEffect(() => {
    if (isOpen) {
      loadRoomTypes();
    }
  }, [isOpen]);

  useEffect(() => {
    if (room) {
      setFormData({
        roomNumber: room.roomNumber,
        typeId: room.type.id,
        floor: room.floor,
        notes: room.notes || ''
      });
    } else {
      setFormData({
        roomNumber: '',
        typeId: '',
        floor: 1,
        notes: ''
      });
    }
    setErrors({});
  }, [room, isOpen]);

  const loadRoomTypes = async () => {
    try {
      setLoadingTypes(true);
      const types = await roomTypeService.getAllRoomTypes();
      setRoomTypes(types);
      
      // Initialize default room types if none exist
      if (types.length === 0) {
        await roomTypeService.initializeDefaultRoomTypes();
        const newTypes = await roomTypeService.getAllRoomTypes();
        setRoomTypes(newTypes);
      }
    } catch (error) {
      console.error('Error loading room types:', error);
    } finally {
      setLoadingTypes(false);
    }
  };

  const validateForm = async (): Promise<boolean> => {
    const newErrors: RoomFormErrors = {};

    if (!formData.roomNumber.trim()) {
      newErrors.roomNumber = 'Room number is required';
    } else {
      // Check if room number already exists (for new rooms or different room)
      const existingRoom = await roomService.getRoomByNumber(formData.roomNumber.trim());
      if (existingRoom && existingRoom.id !== room?.id) {
        newErrors.roomNumber = 'Room number already exists';
      }
    }

    if (!formData.typeId) {
      newErrors.typeId = 'Room type is required';
    }

    if (formData.floor < 1) {
      newErrors.floor = 'Floor must be at least 1';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!(await validateForm())) {
      return;
    }

    setLoading(true);
    try {
      const selectedRoomType = roomTypes.find(type => type.id === formData.typeId);
      if (!selectedRoomType) {
        throw new Error('Selected room type not found');
      }

      const roomData: Room = {
        id: room?.id || `room-${Date.now()}`,
        roomNumber: formData.roomNumber.trim(),
        type: selectedRoomType,
        status: room?.status || RoomStatus.Available,
        ...(room?.currentBookingId && { currentBookingId: room.currentBookingId }),
        ...(formData.notes?.trim() && { notes: formData.notes.trim() }),
        floor: formData.floor,
        createdAt: room?.createdAt || new Date(),
        updatedAt: new Date()
      };

      if (room) {
        await roomService.updateRoom(roomData);
      } else {
        await roomService.addRoom(roomData);
      }

      onSave(roomData);
    } catch (error) {
      console.error('Error saving room:', error);
      alert('Failed to save room. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof RoomFormData, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: undefined
      }));
    }
  };

  if (!isOpen) return null;

  const roomTypeOptions = roomTypes.map(type => ({
    value: type.id,
    label: `${type.name} - $${type.basePrice}/night`
  }));

  const floorOptions = Array.from({ length: 20 }, (_, i) => ({
    value: (i + 1).toString(),
    label: `Floor ${i + 1}`
  }));

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-navy rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
        <Card>
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-poppins font-semibold text-navy-dark dark:text-gold-accent">
              {room ? 'Edit Room' : 'Add New Room'}
            </h2>
            <Button
              variant="secondary"
              onClick={onCancel}
              leftIcon={<XIcon className="w-4 h-4" />}
            >
              Close
            </Button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <Input
              label="Room Number"
              value={formData.roomNumber}
              onChange={(e) => handleInputChange('roomNumber', e.target.value)}
              error={errors.roomNumber}
              placeholder="e.g., 101, A-205"
              required
            />

            <Select
              label="Room Type"
              options={roomTypeOptions}
              value={formData.typeId}
              onChange={(e) => handleInputChange('typeId', e.target.value)}
              error={errors.typeId}
              disabled={loadingTypes}
              required
            />

            <Select
              label="Floor"
              options={floorOptions}
              value={formData.floor.toString()}
              onChange={(e) => handleInputChange('floor', parseInt(e.target.value))}
              error={errors.floor}
              required
            />

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Notes
              </label>
              <textarea
                value={formData.notes || ''}
                onChange={(e) => handleInputChange('notes', e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 dark:border-navy-light rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gold-accent focus:border-transparent bg-white dark:bg-navy-light text-gray-900 dark:text-gray-100"
                placeholder="Optional notes about this room..."
              />
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <Button
                type="button"
                variant="secondary"
                onClick={onCancel}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="primary"
                leftIcon={<SaveIcon className="w-4 h-4" />}
                disabled={loading || loadingTypes}
              >
                {loading ? 'Saving...' : room ? 'Update Room' : 'Add Room'}
              </Button>
            </div>
          </form>
        </Card>
      </div>
    </div>
  );
};

export default RoomForm;
