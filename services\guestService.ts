import { Guest } from '../types';
import { db, STORES } from '../lib/database';

export class GuestService {
  async addGuest(guest: Guest): Promise<void> {
    const guestWithTimestamps = {
      ...guest,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    await db.add(STORES.GUESTS, guestWithTimestamps);
  }

  async updateGuest(guest: Guest): Promise<void> {
    const updatedGuest = {
      ...guest,
      updatedAt: new Date()
    };
    await db.update(STORES.GUESTS, updatedGuest);
  }

  async getGuest(id: string): Promise<Guest | undefined> {
    return await db.get<Guest>(STORES.GUESTS, id);
  }

  async getAllGuests(): Promise<Guest[]> {
    return await db.getAll<Guest>(STORES.GUESTS);
  }

  async deleteGuest(id: string): Promise<void> {
    await db.delete(STORES.GUESTS, id);
  }

  async getGuestByEmail(email: string): Promise<Guest | undefined> {
    const guests = await db.getByIndex<Guest>(STORES.GUESTS, 'email', email);
    return guests[0];
  }

  async getGuestsByLastName(lastName: string): Promise<Guest[]> {
    return await db.getByIndex<Guest>(STORES.GUESTS, 'lastName', lastName);
  }

  async searchGuests(searchTerm: string): Promise<Guest[]> {
    const allGuests = await this.getAllGuests();
    const term = searchTerm.toLowerCase();
    
    return allGuests.filter(guest => 
      guest.firstName.toLowerCase().includes(term) ||
      guest.lastName.toLowerCase().includes(term) ||
      guest.email.toLowerCase().includes(term) ||
      guest.phone.includes(term)
    );
  }

  async getGuestStats(): Promise<{
    total: number;
    withPreferences: number;
    withPastBookings: number;
  }> {
    const allGuests = await this.getAllGuests();
    
    return {
      total: allGuests.length,
      withPreferences: allGuests.filter(g => g.preferences && g.preferences.length > 0).length,
      withPastBookings: allGuests.filter(g => g.pastBookings && g.pastBookings.length > 0).length
    };
  }

  async addPreferenceToGuest(guestId: string, preference: string): Promise<void> {
    const guest = await this.getGuest(guestId);
    if (guest) {
      if (!guest.preferences) {
        guest.preferences = [];
      }
      if (!guest.preferences.includes(preference)) {
        guest.preferences.push(preference);
        await this.updateGuest(guest);
      }
    }
  }

  async removePreferenceFromGuest(guestId: string, preference: string): Promise<void> {
    const guest = await this.getGuest(guestId);
    if (guest && guest.preferences) {
      guest.preferences = guest.preferences.filter(p => p !== preference);
      await this.updateGuest(guest);
    }
  }

  async addBookingToGuestHistory(guestId: string, bookingId: string): Promise<void> {
    const guest = await this.getGuest(guestId);
    if (guest) {
      if (!guest.pastBookings) {
        guest.pastBookings = [];
      }
      if (!guest.pastBookings.includes(bookingId)) {
        guest.pastBookings.push(bookingId);
        await this.updateGuest(guest);
      }
    }
  }
}

export const guestService = new GuestService();
