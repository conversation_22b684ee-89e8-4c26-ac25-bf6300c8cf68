
import React, { useState, useEffect } from 'react';
import { Room, UserRole, RoomStatus, RoomType } from '../types';
import { roomService } from '../services/roomService';
import { roomTypeService } from '../services/roomTypeService';
import { useAsyncOperation } from '../hooks/useAsyncOperation';
import { useNotifications } from '../hooks/useNotifications';
import { useConfirmation } from '../components/shared/ConfirmationDialog';
import { useDebounce } from '../hooks/usePerformance';
import RoomStatusLegend from '../components/rooms/RoomStatusLegend';
import RoomForm from '../components/rooms/RoomForm';
import RoomList from '../components/rooms/RoomList';
import RoomDetails from '../components/rooms/RoomDetails';
import Button from '../components/shared/Button';
import { Input, Select } from '../components/shared/AccessibleForm';
import Card from '../components/shared/Card';
import { LoadingOverlay, Skeleton, ErrorState } from '../components/shared/LoadingStates';
import { PlusIcon, SearchIcon } from '../components/shared/Icon';

interface RoomsPageProps {
  userRole: UserRole;
}

const RoomsPage: React.FC<RoomsPageProps> = ({ userRole: _userRole }) => {
  const [rooms, setRooms] = useState<Room[]>([]);
  const [filteredRooms, setFilteredRooms] = useState<Room[]>([]);
  const [roomTypes, setRoomTypes] = useState<RoomType[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<RoomStatus | ''>('');
  const [typeFilter, setTypeFilter] = useState<string | ''>(''); // RoomType ID

  // Modal states
  const [showForm, setShowForm] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [selectedRoom, setSelectedRoom] = useState<Room | undefined>();

  // Enhanced async operations
  const { state: roomsState, execute: loadRoomsData } = useAsyncOperation<{
    rooms: Room[];
    roomTypes: RoomType[];
  }>(null, {
    retryCount: 3,
    onError: (error) => notifications.error('Failed to load rooms data', error)
  });

  const { state: deleteState, execute: deleteRoom } = useAsyncOperation(null);

  // Notifications and confirmations
  const notifications = useNotifications();
  const { confirm, ConfirmationComponent } = useConfirmation();

  // Debounced search for better performance
  const debouncedSearch = useDebounce((term: string) => {
    setSearchTerm(term);
  }, 300);

  useEffect(() => {
    const fetchData = async () => {
      await loadRoomsData(async () => {
        const [roomsResult, roomTypesResult] = await Promise.all([
          roomService.getAllRooms(),
          roomTypeService.getAllRoomTypes()
        ]);

        if (!roomsResult.success) {
          throw new Error(roomsResult.error || 'Failed to load rooms');
        }

        if (!roomTypesResult.success) {
          throw new Error(roomTypesResult.error || 'Failed to load room types');
        }

        const data = {
          rooms: roomsResult.data || [],
          roomTypes: roomTypesResult.data || []
        };

        setRooms(data.rooms);
        setRoomTypes(data.roomTypes);
        return data;
      });
    };

    fetchData();
  }, [loadRoomsData, notifications]);

  useEffect(() => {
    filterRooms();
  }, [rooms, searchTerm, statusFilter, typeFilter]);

      // Initialize default room types if none exist
      if (allRoomTypes.length === 0) {
        await roomTypeService.initializeDefaultRoomTypes();
        const newRoomTypes = await roomTypeService.getAllRoomTypes();
        setRoomTypes(newRoomTypes);
      }
    } catch (error) {
      console.error('Error loading rooms data:', error);
    } finally {
      setLoading(false);
    }
  };

  const filterRooms = () => {
    let filtered = rooms;

    if (searchTerm.trim()) {
      filtered = filtered.filter(room =>
        room.roomNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        room.type.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter) {
      filtered = filtered.filter(room => room.status === statusFilter);
    }

    if (typeFilter) {
      filtered = filtered.filter(room => room.type.id === typeFilter);
    }

    setFilteredRooms(filtered);
  };

  const handleAddRoom = () => {
    setSelectedRoom(undefined);
    setShowForm(true);
  };

  const handleEditRoom = (roomId: string) => {
    const room = rooms.find(r => r.id === roomId);
    setSelectedRoom(room);
    setShowForm(true);
  };

  const handleViewDetails = (roomId: string) => {
    const room = rooms.find(r => r.id === roomId);
    setSelectedRoom(room);
    setShowDetails(true);
  };

  /**
   * Enhanced delete room with confirmation and error handling
   */
  const handleDeleteRoom = async (roomId: string) => {
    const room = rooms.find(r => r.id === roomId);
    if (!room) return;

    const confirmed = await confirm({
      title: 'Delete Room',
      message: `Are you sure you want to delete room ${room.roomNumber}? This action cannot be undone.`,
      type: 'delete',
      confirmText: 'Delete Room',
      dangerous: true
    });

    if (!confirmed) return;

    await deleteRoom(async () => {
      const result = await roomService.deleteRoom(roomId);
      if (!result.success) {
        throw new Error(result.error || 'Failed to delete room');
      }

      // Refresh data after successful deletion
      const roomsResult = await roomService.getAllRooms();
      if (roomsResult.success && roomsResult.data) {
        setRooms(roomsResult.data);
        notifications.success('Room deleted successfully', `Room ${room.roomNumber} has been deleted.`);
      }

      return result;
    });
  };

  const handleUpdateStatus = async (roomId: string, newStatus: RoomStatus) => {
    try {
      await roomService.updateRoomStatus(roomId, newStatus);
      await loadData();
    } catch (error) {
      console.error('Error updating room status:', error);
      alert('Failed to update room status');
    }
  };

  const handleSaveRoom = async () => {
    setShowForm(false);
    setSelectedRoom(undefined);
    await loadData();
  };

  const handleCancelForm = () => {
    setShowForm(false);
    setSelectedRoom(undefined);
  };

  const handleCloseDetails = () => {
    setShowDetails(false);
    setSelectedRoom(undefined);
  };

  const handleEditFromDetails = () => {
    setShowDetails(false);
    setShowForm(true);
  };

  const handleUpdateStatusFromDetails = async (status: RoomStatus) => {
    if (selectedRoom) {
      await handleUpdateStatus(selectedRoom.id, status);
      // Update the selected room for the details modal
      const updatedRoom = rooms.find(r => r.id === selectedRoom.id);
      if (updatedRoom) {
        setSelectedRoom(updatedRoom);
      }
    }
  };
  
  const statusOptions = [{ value: '', label: 'All Statuses' }, ...Object.values(RoomStatus).map(s => ({ value: s, label: s }))];
  const typeOptions = [{ value: '', label: 'All Types' }, ...roomTypes.map(rt => ({ value: rt.id, label: rt.name }))];

  // Enhanced loading state with skeleton
  if (roomsState.loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <Skeleton width={200} height={32} />
          <Skeleton width={120} height={40} />
        </div>

        <Card>
          <div className="space-y-4">
            <div className="flex gap-4">
              <Skeleton width={300} height={40} />
              <Skeleton width={150} height={40} />
              <Skeleton width={150} height={40} />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {Array.from({ length: 6 }).map((_, index) => (
                <Skeleton key={index} variant="rectangular" height={200} />
              ))}
            </div>
          </div>
        </Card>
      </div>
    );
  }

  // Enhanced error state
  if (roomsState.error) {
    return (
      <ErrorState
        title="Failed to Load Rooms"
        description="We couldn't load the rooms data. Please try again."
        error={roomsState.error}
        onRetry={() => window.location.reload()}
      />
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <div className="flex flex-col md:flex-row justify-between items-center gap-4 mb-6">
          <h2 className="text-2xl font-poppins font-semibold text-navy-dark dark:text-gold-accent">
            Room Management
          </h2>
          <Button
            onClick={handleAddRoom}
            leftIcon={<PlusIcon className="w-5 h-5"/>}
            variant="primary"
          >
            Add New Room
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6 p-4 bg-light-gray dark:bg-navy-light rounded-lg">
          <Input
            placeholder="Search by room number or type..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            icon={<SearchIcon className="w-5 h-5"/>}
          />
          <Select
            options={statusOptions}
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value as RoomStatus | '')}
            placeholder="Filter by status"
          />
          <Select
            options={typeOptions}
            value={typeFilter}
            onChange={(e) => setTypeFilter(e.target.value as string | '')}
            placeholder="Filter by type"
          />
        </div>

        <RoomList
          rooms={filteredRooms}
          onViewDetails={handleViewDetails}
          onEdit={handleEditRoom}
          onDelete={handleDeleteRoom}
          onUpdateStatus={handleUpdateStatus}
          loading={loading}
        />
      </Card>

      <RoomStatusLegend />

      {/* Room Form Modal */}
      <RoomForm
        room={selectedRoom}
        onSave={handleSaveRoom}
        onCancel={handleCancelForm}
        isOpen={showForm}
      />

      {/* Room Details Modal */}
      {selectedRoom && (
        <RoomDetails
          room={selectedRoom}
          onClose={handleCloseDetails}
          onEdit={handleEditFromDetails}
          onUpdateStatus={handleUpdateStatusFromDetails}
          isOpen={showDetails}
        />
      )}

      {/* Global Confirmation Dialog */}
      {ConfirmationComponent}
    </div>
  );
};

export default RoomsPage;
