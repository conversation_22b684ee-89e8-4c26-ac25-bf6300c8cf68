
import React from 'react';
import { useTheme } from '../../hooks/useThemeHook';
import { SunIcon, MoonIcon } from './Icon';
import Button from './Button';

const ThemeToggle: React.FC = () => {
  const { theme, toggleTheme } = useTheme();

  return (
    <Button
      onClick={toggleTheme}
      variant="ghost"
      size="sm"
      className="p-2 rounded-full text-dark-gray dark:text-light-gray hover:bg-gray-200 dark:hover:bg-navy-light"
      aria-label={theme === 'light' ? 'Switch to dark mode' : 'Switch to light mode'}
    >
      {theme === 'light' ? (
        <MoonIcon className="w-5 h-5" />
      ) : (
        <SunIcon className="w-5 h-5" />
      )}
    </Button>
  );
};

export default ThemeToggle;
