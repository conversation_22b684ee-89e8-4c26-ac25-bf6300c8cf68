# معايير وقواعد مشروع إدارة الفنادق (Hotel Management Project Standards)

## نظرة عامة (Overview)
هذا المستند يحدد المعايير والقواعد الأساسية لمشروع إدارة الفنادق Premium Stay Hotel Management. يجب على جميع المطورين اتباع هذه المعايير لضمان جودة الكود وسهولة الصيانة.

## 🎯 متطلبات المشروع الأساسية (Core Project Requirements)

### قاعدة البيانات
- **استخدام IndexedDB فقط**: المشروع مصمم للعمل بشكل كامل offline بدون أي dependencies خارجية
- **لا توجد بيانات وهمية**: يجب حذف جميع البيانات الوهمية من المشروع نهائياً
- **التخزين المحلي**: جميع البيانات يجب أن تُحفظ محلياً في المتصفح

### الهدف من المشروع
- مشروع portfolio showcase
- يعمل بشكل كامل offline
- لا يحتاج إلى server أو API خارجي

## 🏗️ هيكلية المشروع (Project Structure)

```
├── components/          # مكونات UI قابلة لإعادة الاستخدام
│   ├── layout/         # مكونات التخطيط (Header, Sidebar)
│   ├── shared/         # مكونات مشتركة (Button, Card, Input)
│   ├── dashboard/      # مكونات خاصة بلوحة التحكم
│   ├── bookings/       # مكونات إدارة الحجوزات
│   ├── rooms/          # مكونات إدارة الغرف
│   └── housekeeping/   # مكونات إدارة التنظيف
├── pages/              # صفحات التطبيق
├── services/           # منطق العمل وخدمات البيانات
├── lib/                # وظائف مساعدة وإعداد قاعدة البيانات
├── hooks/              # React hooks مخصصة
├── src/styles/         # أنماط عامة و Tailwind CSS
├── types.ts            # تعريفات TypeScript
├── constants.ts        # ثوابت التطبيق
└── App.tsx            # المكون الرئيسي
```

## 🛠️ التقنيات المستخدمة (Tech Stack)

### Frontend Framework
- **React 18.3.1** مع **TypeScript 5.7.2**
- **React Router DOM 7.6.2** للتنقل
- **Vite 6.2.0** كأداة البناء

### UI & Styling
- **Tailwind CSS 3.4.17** للتنسيق
- **Radix UI** للمكونات الأساسية
- **Lucide React** للأيقونات
- **Recharts 2.12.7** للرسوم البيانية

### Form Handling & Validation
- **React Hook Form** لإدارة النماذج
- **Zod** للتحقق من صحة البيانات
- **Custom Form Validation Hooks** للتحقق المتقدم

### Database
- **IndexedDB** للتخزين المحلي مع تحسينات الأداء

### State Management & Performance
- **Custom React Hooks** لإدارة الحالة
- **Performance Monitoring** لمراقبة الأداء
- **Virtual Scrolling** للقوائم الكبيرة
- **Lazy Loading** للصور والمكونات

### User Experience
- **Accessibility (a11y)** دعم كامل لإمكانية الوصول
- **Loading States** حالات التحميل المتقدمة
- **Error Handling** معالجة شاملة للأخطاء
- **Notifications System** نظام إشعارات متطور

### Code Quality
- **ESLint** للتحقق من جودة الكود
- **Prettier** لتنسيق الكود
- **TypeScript** للتحقق من الأنواع مع أنماط متقدمة

## 📝 معايير الكود (Coding Standards)

### TypeScript - Modern Patterns
- استخدم TypeScript في جميع الملفات مع أنماط حديثة
- استخدم `const assertions` بدلاً من enums للقيم الثابتة
- عرّف interfaces واضحة مع `readonly` للبيانات غير القابلة للتغيير
- استخدم `BaseEntity` للكيانات المشتركة
- تجنب استخدام `any` type نهائياً

```typescript
// ✅ صحيح - استخدام const assertions
export const UserRole = {
  Admin: 'Admin',
  FrontDesk: 'Front Desk',
  Manager: 'Manager',
} as const;

export type UserRole = typeof UserRole[keyof typeof UserRole];

// ✅ صحيح - BaseEntity pattern
interface BaseEntity {
  readonly id: string;
  readonly createdAt: Date;
  readonly updatedAt: Date;
}

interface Guest extends BaseEntity {
  firstName: string;
  lastName: string;
  readonly email: string; // Immutable for data integrity
}

// ❌ خطأ
enum UserRole { Admin, Manager } // استخدم const assertions
const guest: any = { ... }; // تجنب any
```

### React Components - Modern Patterns
- استخدم Functional Components مع hooks حديثة
- اتبع نمط PascalCase لأسماء المكونات
- استخدم TypeScript interfaces للـ props مع JSDoc
- استخدم `forwardRef` للمكونات التي تحتاج refs
- استخدم `memo` للمكونات التي تحتاج تحسين الأداء
- استخدم custom hooks لمنطق قابل لإعادة الاستخدام

```typescript
// ✅ صحيح - Modern component with JSDoc
/**
 * Enhanced button component with accessibility features
 * @param variant - Button style variant
 * @param onClick - Click handler function
 * @param children - Button content
 */
interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'danger';
  onClick: () => void;
  children: React.ReactNode;
  disabled?: boolean;
  loading?: boolean;
  'aria-label'?: string;
}

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  ({ variant = 'primary', onClick, children, disabled, loading, ...props }, ref) => {
    return (
      <button
        ref={ref}
        className={cn('btn', `btn-${variant}`, { 'btn-loading': loading })}
        onClick={onClick}
        disabled={disabled || loading}
        aria-disabled={disabled || loading}
        {...props}
      >
        {loading ? <Spinner size="sm" /> : children}
      </button>
    );
  }
);

Button.displayName = 'Button';
```

### File Naming
- **Components**: PascalCase (e.g., `UserProfile.tsx`)
- **Hooks**: camelCase with "use" prefix (e.g., `useTheme.tsx`)
- **Services**: camelCase (e.g., `roomService.ts`)
- **Types**: camelCase (e.g., `types.ts`)

### Import/Export
- استخدم named exports للمكونات
- رتب imports بالترتيب التالي:
  1. React imports
  2. Third-party libraries
  3. Internal components/hooks
  4. Types
  5. Relative imports

```typescript
// ✅ صحيح
import React from 'react';
import { useNavigate } from 'react-router-dom';
import Button from '../shared/Button';
import { Guest } from '../../types';
import './styles.css';
```

## 🎨 معايير التصميم (Design Standards)

### Color Palette
```css
/* Primary Colors */
--navy: #1A2E4C;           /* الأزرق الداكن الأساسي */
--navy-light: #2D4A70;     /* الأزرق الفاتح */
--navy-dark: #0F1A2A;      /* الأزرق الداكن جداً */
--gold-accent: #DAA520;    /* الذهبي للتمييز */

/* Neutral Colors */
--light-gray: #F3F4F6;     /* خلفية فاتحة */
--medium-gray: #D1D5DB;    /* حدود وفواصل */
--dark-gray: #4B5563;      /* نص في الوضع الفاتح */

/* Status Colors */
--status-green: #10B981;   /* نجاح، متاح */
--status-red: #EF4444;     /* خطأ، مشغول */
--status-yellow: #F59E0B;  /* تحذير، تنظيف */
--status-blue: #3B82F6;    /* معلومات */
```

### Tailwind CSS Classes
- استخدم Tailwind classes بدلاً من CSS مخصص
- اتبع نظام الألوان المحدد
- استخدم responsive design classes

```tsx
// ✅ صحيح
<div className="bg-light-gray dark:bg-navy p-4 rounded-lg shadow-md">
  <h2 className="text-dark-gray dark:text-light-gray text-xl font-semibold">
    Title
  </h2>
</div>
```

### Dark Mode Support
- جميع المكونات يجب أن تدعم الوضع الداكن
- استخدم `dark:` prefix في Tailwind
- اختبر المكونات في كلا الوضعين

## 🗃️ إدارة البيانات (Data Management)

### Enhanced IndexedDB Structure
```typescript
// Enhanced store definitions with type safety
export const STORES = {
  ROOM_TYPES: 'roomTypes',
  ROOMS: 'rooms',
  GUESTS: 'guests',
  BOOKINGS: 'bookings',
  HOUSEKEEPING_TASKS: 'housekeepingTasks',
  STAFF: 'staff',
  SETTINGS: 'settings'
} as const;

export type StoreName = typeof STORES[keyof typeof STORES];

// Enhanced database operations with error handling
export interface DatabaseResult<T> {
  success: boolean;
  data?: T;
  error?: string;
}

export class DatabaseError extends Error {
  constructor(
    message: string,
    public readonly operation: string,
    public readonly store?: string,
    public readonly originalError?: Error
  ) {
    super(message);
    this.name = 'DatabaseError';
  }
}
```

### Service Layer Pattern - Enhanced
- استخدم service files مع معالجة شاملة للأخطاء
- فصل منطق العمل عن UI components
- استخدم async/await مع try/catch blocks
- استخدم DatabaseResult للنتائج المنظمة
- استخدم performance monitoring للعمليات البطيئة

```typescript
// Enhanced roomService.ts
export class RoomService {
  static async getAllRooms(): Promise<DatabaseResult<Room[]>> {
    try {
      const rooms = await db.getAll<Room>(STORES.ROOMS);
      return { success: true, data: rooms };
    } catch (error) {
      return {
        success: false,
        error: error instanceof DatabaseError ? error.message : 'Failed to fetch rooms'
      };
    }
  }

  static async updateRoomStatus(
    roomId: string,
    status: RoomStatus
  ): Promise<DatabaseResult<void>> {
    try {
      const result = await db.get<Room>(STORES.ROOMS, roomId);
      if (!result.success || !result.data) {
        return { success: false, error: 'Room not found' };
      }

      const updatedRoom = { ...result.data, status, updatedAt: new Date() };
      return await db.update(STORES.ROOMS, updatedRoom);
    } catch (error) {
      return {
        success: false,
        error: error instanceof DatabaseError ? error.message : 'Failed to update room'
      };
    }
  }
}
```

## 🧪 معايير الاختبار (Testing Standards)

### Unit Testing
- اكتب tests للمكونات المهمة
- اختبر business logic في services
- استخدم meaningful test descriptions

### Manual Testing
- اختبر جميع المكونات في كلا الوضعين (فاتح/داكن)
- تأكد من responsive design
- اختبر offline functionality

## 📦 إدارة التبعيات (Dependency Management)

### Package Management
- استخدم npm للتبعيات
- لا تضف تبعيات غير ضرورية
- حافظ على package.json نظيف

### Version Control
- استخدم semantic versioning
- اكتب commit messages واضحة
- استخدم feature branches

## 🔧 أدوات التطوير (Development Tools)

### ESLint Configuration
```javascript
// eslint.config.js
rules: {
  '@typescript-eslint/no-unused-vars': ['error', { argsIgnorePattern: '^_' }],
  'react/prop-types': 'off',
  'react-refresh/only-export-components': ['warn', { allowConstantExport: true }],
}
```

### Prettier Configuration
```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false
}
```

## 🎯 الأنماط الحديثة (Modern Patterns)

### Custom Hooks للحالة المتقدمة
- استخدم `useAsyncOperation` للعمليات غير المتزامنة
- استخدم `useFormValidation` للنماذج المعقدة
- استخدم `useNotifications` لإدارة الإشعارات
- استخدم `usePerformance` لمراقبة الأداء

```typescript
// ✅ مثال على استخدام useAsyncOperation
const { state, execute, retry } = useAsyncOperation<Room[]>([], {
  retryCount: 3,
  onError: (error) => notifications.error('Failed to load rooms', error)
});

// تنفيذ العملية
await execute(() => roomService.getAllRooms());
```

### Error Handling المتقدم
- استخدم `DatabaseError` للأخطاء المخصصة
- استخدم `useErrorHandler` للمعالجة الشاملة
- استخدم `try/catch` مع logging مناسب
- استخدم user-friendly error messages

```typescript
// ✅ معالجة أخطاء شاملة
const { handleAsyncError } = useErrorHandler();

const loadData = async () => {
  const result = await handleAsyncError(
    () => roomService.getAllRooms(),
    'Room Management',
    { persistent: true }
  );

  if (result?.success) {
    setRooms(result.data);
  }
};
```

### Accessibility (a11y) Standards
- استخدم semantic HTML elements
- أضف ARIA labels و descriptions
- استخدم focus management للـ modals
- اختبر مع screen readers
- استخدم keyboard navigation

```typescript
// ✅ مثال على accessibility
<FormField
  label="Guest Email"
  error={errors.email}
  hint="We'll use this to send booking confirmations"
  required
>
  <Input
    type="email"
    value={email}
    onChange={setEmail}
    aria-describedby="email-hint email-error"
    aria-invalid={!!errors.email}
  />
</FormField>
```

## 🚀 معايير الأداء (Performance Standards)

### Code Splitting & Lazy Loading
- استخدم lazy loading للصفحات والمكونات
- استخدم `useIntersectionObserver` للتحميل عند الحاجة
- استخدم `useVirtualScroll` للقوائم الكبيرة
- قسم المكونات الكبيرة إلى أجزاء أصغر

### Performance Monitoring
- استخدم `usePerformanceMonitor` لقياس الأداء
- راقب العمليات البطيئة (> 100ms)
- استخدم `useMemoryMonitor` لمراقبة الذاكرة
- استخدم `useRenderTracker` في التطوير

### Optimization Techniques
- استخدم `React.memo` للمكونات الثقيلة
- استخدم `useCallback` و `useMemo` بحكمة
- استخدم `useDebounce` للبحث والتصفية
- استخدم `useThrottle` للأحداث المتكررة

### Bundle Size
- راقب حجم bundle باستمرار
- استخدم tree shaking
- تجنب التبعيات الثقيلة غير الضرورية
- استخدم dynamic imports للمكونات الكبيرة

## 📋 قائمة المراجعة المحدثة (Enhanced Checklist)

قبل إضافة أي feature جديدة، تأكد من:

### الأساسيات (Basics)
- [ ] البحث عن مكونات مشابهة موجودة
- [ ] اتباع نمط التسمية المحدد
- [ ] استخدام TypeScript interfaces مع JSDoc
- [ ] عدم إضافة بيانات وهمية
- [ ] استخدام IndexedDB للتخزين

### TypeScript & Code Quality
- [ ] استخدام const assertions بدلاً من enums
- [ ] استخدام BaseEntity للكيانات
- [ ] تجنب any type نهائياً
- [ ] إضافة proper error handling
- [ ] استخدام DatabaseResult للعمليات

### React Patterns
- [ ] استخدام forwardRef عند الحاجة
- [ ] استخدام memo للمكونات الثقيلة
- [ ] استخدام custom hooks للمنطق المشترك
- [ ] معالجة loading states
- [ ] معالجة error states

### Accessibility (a11y)
- [ ] استخدام semantic HTML
- [ ] إضافة ARIA labels
- [ ] دعم keyboard navigation
- [ ] اختبار مع screen readers
- [ ] إضافة focus management

### Performance
- [ ] استخدام lazy loading عند الحاجة
- [ ] تجنب re-renders غير الضرورية
- [ ] استخدام debounce/throttle للأحداث
- [ ] مراقبة العمليات البطيئة
- [ ] تحسين bundle size

### User Experience
- [ ] دعم الوضع الداكن
- [ ] اتباع نظام الألوان
- [ ] التأكد من responsive design
- [ ] إضافة loading indicators
- [ ] إضافة confirmation dialogs للعمليات المهمة
- [ ] إضافة proper notifications

### Testing & Documentation
- [ ] كتابة JSDoc للمكونات والدوال
- [ ] اختبار المكون في كلا الوضعين
- [ ] اختبار على أجهزة مختلفة
- [ ] توثيق أي patterns جديدة
- [ ] تحديث PROJECT_STANDARDS.md عند الحاجة

## 🔄 عملية التطوير (Development Process)

1. **التحليل**: فهم المتطلبات وتحليل الكود الموجود
2. **التصميم**: تخطيط المكونات والبيانات المطلوبة
3. **التطوير**: كتابة الكود وفقاً للمعايير
4. **الاختبار**: اختبار المكونات والوظائف
5. **المراجعة**: مراجعة الكود والتأكد من المعايير
6. **التوثيق**: تحديث التوثيق إذا لزم الأمر

## 🆕 التحسينات الحديثة (Recent Improvements)

### تم تطبيق التحسينات التالية (2025):

#### TypeScript Enhancements
- ✅ تحويل enums إلى const assertions للأداء الأفضل
- ✅ إضافة BaseEntity pattern للكيانات المشتركة
- ✅ تحسين type safety مع readonly properties
- ✅ إضافة comprehensive error types

#### IndexedDB Optimization
- ✅ تحسين database operations مع error handling
- ✅ إضافة transaction management
- ✅ تحسين indexes للاستعلامات الأسرع
- ✅ إضافة DatabaseResult pattern

#### React Architecture
- ✅ إضافة custom hooks متقدمة (useAsyncOperation, useFormValidation)
- ✅ تحسين component patterns مع forwardRef و memo
- ✅ إضافة performance monitoring hooks
- ✅ تحسين state management patterns

#### User Experience
- ✅ إضافة comprehensive loading states
- ✅ تحسين accessibility مع ARIA support
- ✅ إضافة notification system
- ✅ تحسين form validation مع user feedback
- ✅ إضافة confirmation dialogs

#### Performance Optimization
- ✅ إضافة virtual scrolling للقوائم الكبيرة
- ✅ تحسين lazy loading patterns
- ✅ إضافة debounce/throttle utilities
- ✅ تحسين memory management

#### Code Quality
- ✅ إضافة comprehensive JSDoc documentation
- ✅ تحسين error handling patterns
- ✅ إضافة performance monitoring
- ✅ تحديث coding standards

### الملفات الجديدة المضافة:
- `hooks/useAsyncOperation.ts` - إدارة العمليات غير المتزامنة
- `hooks/useFormValidation.ts` - تحقق متقدم من النماذج
- `hooks/useNotifications.ts` - نظام الإشعارات
- `hooks/usePerformance.ts` - مراقبة الأداء
- `components/shared/LoadingStates.tsx` - حالات التحميل
- `components/shared/AccessibleForm.tsx` - نماذج محسنة
- `components/shared/ConfirmationDialog.tsx` - حوارات التأكيد

---

**ملاحظة**: هذا المستند يُحدث بانتظام مع تطور المشروع وإضافة معايير جديدة. آخر تحديث: يناير 2025
