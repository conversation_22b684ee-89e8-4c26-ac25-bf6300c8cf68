import React, { SVGProps } from 'react';

/**
 * User roles in the hotel management system
 * @description Defines the different types of users and their access levels
 */
export const UserRole = {
  Admin: 'Admin',
  FrontDesk: 'Front Desk',
  Housekeeping: 'Housekeeping',
  Manager: 'Manager',
  Guest: 'Guest', // For potential future portal
} as const;

export type UserRole = typeof UserRole[keyof typeof UserRole];

/**
 * Room status enumeration
 * @description Tracks the current state of hotel rooms
 */
export const RoomStatus = {
  Available: 'Available',
  Occupied: 'Occupied',
  Cleaning: 'Cleaning',
  OutOfService: 'Out of Service',
  PendingArrival: 'Pending Arrival',
  PendingDeparture: 'Pending Departure',
} as const;

export type RoomStatus = typeof RoomStatus[keyof typeof RoomStatus];

/**
 * Booking status enumeration
 * @description Tracks the lifecycle of hotel bookings
 */
export const BookingStatus = {
  Confirmed: 'Confirmed',
  Pending: 'Pending Payment',
  Cancelled: 'Cancelled',
  CheckedIn: 'Checked In',
  CheckedOut: 'Checked Out',
} as const;

export type BookingStatus = typeof BookingStatus[keyof typeof BookingStatus];

/**
 * Base interface for entities with timestamps
 * @description Common fields for all database entities
 */
export interface BaseEntity {
  readonly id: string;
  readonly createdAt: Date;
  readonly updatedAt: Date;
}

/**
 * Room type configuration
 * @description Defines different categories of rooms available in the hotel
 */
export interface RoomType extends BaseEntity {
  readonly name: string; // e.g., Single, Double, Suite
  readonly basePrice: number;
  readonly amenities: readonly string[];
  readonly capacity: number;
  readonly description?: string;
}

/**
 * Hotel room entity
 * @description Represents a physical room in the hotel
 */
export interface Room extends BaseEntity {
  readonly roomNumber: string;
  readonly type: RoomType;
  status: RoomStatus;
  currentBookingId?: string | null;
  notes?: string;
  readonly floor: number;
  readonly isAccessible?: boolean;
  readonly maintenanceNotes?: string;
}

/**
 * Guest profile
 * @description Customer information and preferences
 */
export interface Guest extends BaseEntity {
  firstName: string;
  lastName: string;
  readonly email: string; // Email should be immutable for data integrity
  phone: string;
  address?: string;
  preferences?: readonly string[];
  readonly pastBookings?: readonly string[]; // Array of booking IDs
  readonly loyaltyPoints?: number;
  readonly vipStatus?: boolean;
}

/**
 * Hotel booking/reservation
 * @description Represents a guest's reservation for a room
 */
export interface Booking extends BaseEntity {
  readonly guest: Guest;
  readonly room: Room;
  readonly checkInDate: Date;
  readonly checkOutDate: Date;
  readonly numberOfGuests: number;
  readonly totalAmount: number;
  status: BookingStatus;
  notes?: string;
  readonly specialRequests?: readonly string[];
  readonly paymentStatus?: 'Pending' | 'Partial' | 'Paid' | 'Refunded';
}

/**
 * Housekeeping task types
 */
export const HousekeepingTaskType = {
  Clean: 'Clean',
  Maintenance: 'Maintenance',
  Inspect: 'Inspect',
  DeepClean: 'Deep Clean',
} as const;

export type HousekeepingTaskType = typeof HousekeepingTaskType[keyof typeof HousekeepingTaskType];

/**
 * Task status enumeration
 */
export const TaskStatus = {
  Pending: 'Pending',
  InProgress: 'In Progress',
  Completed: 'Completed',
  Blocked: 'Blocked',
  Cancelled: 'Cancelled',
} as const;

export type TaskStatus = typeof TaskStatus[keyof typeof TaskStatus];

/**
 * Priority levels
 */
export const Priority = {
  Low: 'Low',
  Medium: 'Medium',
  High: 'High',
  Critical: 'Critical',
} as const;

export type Priority = typeof Priority[keyof typeof Priority];

/**
 * Housekeeping task
 * @description Represents cleaning and maintenance tasks for rooms
 */
export interface HousekeepingTask extends BaseEntity {
  readonly roomId: string;
  readonly roomNumber: string;
  readonly taskType: HousekeepingTaskType;
  assignedTo?: string; // Staff ID
  status: TaskStatus;
  priority: Priority;
  description?: string;
  readonly reportedAt: Date;
  completedAt?: Date;
  readonly estimatedDuration?: number; // in minutes
  readonly actualDuration?: number; // in minutes
}

export interface ReportDataPoint {
  date: string; // or Date object
  value: number;
}

export interface OccupancyReport {
  daily: ReportDataPoint[];
  weekly: ReportDataPoint[];
  monthly: ReportDataPoint[];
}

export interface RevenueReport {
  byRoomType: { typeName: string; revenue: number }[];
  byTimePeriod: ReportDataPoint[];
}

export interface StaffMember {
  id: string;
  name: string;
  role: UserRole;
  email: string;
}

// For navigation items
export interface NavItem {
  path: string;
  name: string;
  icon: (props: SVGProps<SVGSVGElement>) => React.JSX.Element;
  allowedRoles: UserRole[];
}

export type Theme = 'light' | 'dark';

export interface ThemeContextType {
  theme: Theme;
  toggleTheme: () => void;
}

// Form interfaces for creating/editing entities
export interface GuestFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address?: string;
  preferences?: string[];
}

export interface RoomFormData {
  roomNumber: string;
  typeId: string;
  floor: number;
  notes?: string;
}

export interface BookingFormData {
  guestId: string;
  roomId: string;
  checkInDate: string; // ISO date string for form inputs
  checkOutDate: string; // ISO date string for form inputs
  numberOfGuests: number;
  notes?: string;
}

// Form error interfaces
export interface GuestFormErrors {
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  address?: string;
  preferences?: string;
}

export interface RoomFormErrors {
  roomNumber?: string;
  typeId?: string;
  floor?: string;
  notes?: string;
}

export interface BookingFormErrors {
  guestId?: string;
  roomId?: string;
  checkInDate?: string;
  checkOutDate?: string;
  numberOfGuests?: string;
  notes?: string;
}

// Search and filter interfaces
export interface SearchFilters {
  searchTerm?: string;
  status?: string;
  dateFrom?: string;
  dateTo?: string;
}

// Statistics interfaces
export interface DashboardStats {
  totalGuests: number;
  totalRooms: number;
  totalBookings: number;
  occupancyRate: number;
  revenue: number;
  arrivalsToday: number;
  departuresToday: number;
}