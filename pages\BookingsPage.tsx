
import React, { useState, useEffect, useMemo } from 'react';
import { Booking, UserRole, BookingStatus } from '../types';
import { bookingService } from '../services/bookingService';
import BookingListItem from '../components/bookings/BookingListItem';
import BookingForm from '../components/bookings/BookingForm';
import BookingDetails from '../components/bookings/BookingDetails';
import Button from '../components/shared/Button';
import Input from '../components/shared/Input';
import Select from '../components/shared/Select';
import Card from '../components/shared/Card';
import { PlusIcon, SearchIcon } from '../components/shared/Icon';

interface BookingsPageProps {
  userRole: UserRole;
}

const BookingsPage: React.FC<BookingsPageProps> = ({ userRole: _userRole }) => {
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<BookingStatus | ''>('');
  const [dateFilter, setDateFilter] = useState(''); // Stores "YYYY-MM-DD"

  // Modal states
  const [showForm, setShowForm] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [selectedBooking, setSelectedBooking] = useState<Booking | undefined>();

  useEffect(() => {
    const loadBookings = async () => {
      try {
        const allBookings = await bookingService.getAllBookings();
        setBookings(allBookings);
      } catch (error) {
        console.error('Error loading bookings:', error);
      } finally {
        setLoading(false);
      }
    };

    loadBookings();
  }, []);

  // Action handlers
  const handleViewDetails = (bookingId: string) => {
    const booking = bookings.find(b => b.id === bookingId);
    setSelectedBooking(booking);
    setShowDetails(true);
  };

  const handleEditBooking = (bookingId: string) => {
    const booking = bookings.find(b => b.id === bookingId);
    setSelectedBooking(booking);
    setShowForm(true);
  };

  const handleCancelBooking = async (bookingId: string) => {
    if (window.confirm(`Are you sure you want to cancel booking ${bookingId}?`)) {
      try {
        await bookingService.cancelBooking(bookingId);
        // Reload bookings
        const updatedBookings = await bookingService.getAllBookings();
        setBookings(updatedBookings);
        alert(`Booking ${bookingId} cancelled.`);
      } catch (error) {
        console.error('Error cancelling booking:', error);
        alert('Failed to cancel booking');
      }
    }
  };

  const handleAddNewBooking = () => {
    setSelectedBooking(undefined);
    setShowForm(true);
  };

  const handleSaveBooking = async () => {
    setShowForm(false);
    setSelectedBooking(undefined);
    // Reload bookings
    const updatedBookings = await bookingService.getAllBookings();
    setBookings(updatedBookings);
  };

  const handleCancelForm = () => {
    setShowForm(false);
    setSelectedBooking(undefined);
  };

  const handleCloseDetails = () => {
    setShowDetails(false);
    setSelectedBooking(undefined);
  };

  const handleEditFromDetails = () => {
    setShowDetails(false);
    setShowForm(true);
  };

  const handleStatusUpdate = async (updatedBooking: Booking) => {
    // Update the booking in the list
    const updatedBookings = await bookingService.getAllBookings();
    setBookings(updatedBookings);
    setSelectedBooking(updatedBooking);
  };

  const normalizeDate = (date: Date): Date => {
    const newDate = new Date(date);
    newDate.setHours(0, 0, 0, 0);
    return newDate;
  };

  const filteredBookings = useMemo(() => {
    return bookings.filter(booking => {
      const guestName = `${booking.guest.firstName} ${booking.guest.lastName}`.toLowerCase();
      const roomNumber = booking.room.roomNumber.toLowerCase();
      const matchesSearch = guestName.includes(searchTerm.toLowerCase()) || roomNumber.includes(searchTerm.toLowerCase());
      const matchesStatus = statusFilter ? booking.status === statusFilter : true;
      
      let matchesDate = true;
      if (dateFilter) { // dateFilter is "YYYY-MM-DD"
        try {
          const dateParts = dateFilter.split('-').map(Number);
          // Construct date as local midnight to avoid timezone issues with `new Date(string)`
          const selectedDate = normalizeDate(new Date(dateParts[0], dateParts[1] - 1, dateParts[2]));
          
          const bookingCheckIn = normalizeDate(booking.checkInDate);
          const bookingCheckOut = normalizeDate(booking.checkOutDate);
          
          matchesDate = selectedDate.getTime() >= bookingCheckIn.getTime() && selectedDate.getTime() <= bookingCheckOut.getTime();
        } catch (e) {
          console.error("Error parsing date filter:", e);
          matchesDate = true; // Fallback to not filtering by date if parsing fails
        }
      }
      
      return matchesSearch && matchesStatus && matchesDate;
    });
  }, [bookings, searchTerm, statusFilter, dateFilter]);
  
  const statusOptions = [{ value: '', label: 'All Statuses' }, ...Object.values(BookingStatus).map(s => ({ value: s, label: s }))];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <div className="flex flex-col md:flex-row justify-between items-center gap-4 mb-6">
          <h2 className="text-2xl font-poppins font-semibold text-navy-dark dark:text-gold-accent">Manage Bookings</h2>
          <Button onClick={handleAddNewBooking} leftIcon={<PlusIcon className="w-5 h-5"/>} variant="primary">
            New Booking
          </Button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6 p-4 bg-light-gray dark:bg-navy-light rounded-lg">
          <Input 
            placeholder="Search by guest or room..." 
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            icon={<SearchIcon className="w-5 h-5"/>}
          />
          <Select
            options={statusOptions}
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value as BookingStatus | '')}
            placeholder="Filter by status"
          />
          <Input 
            type="date"
            // The label for date input is often implicitly understood or can be added if needed
            // label="Filter by Date" 
            value={dateFilter}
            onChange={(e) => setDateFilter(e.target.value)}
          />
        </div>

        <div className="overflow-x-auto shadow-md rounded-lg">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-navy-light">
            <thead className="bg-gray-50 dark:bg-navy">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">ID</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Guest</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Room</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Check-in</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Check-out</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider text-right">Total</th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-navy divide-y divide-gray-200 dark:divide-navy-light">
              {filteredBookings.length > 0 ? (
                filteredBookings.map(booking => (
                  <BookingListItem 
                    key={booking.id} 
                    booking={booking} 
                    onViewDetails={handleViewDetails}
                    onEdit={handleEditBooking}
                    onCancel={handleCancelBooking}
                  />
                ))
              ) : (
                <tr>
                  <td colSpan={8} className="px-6 py-12 text-center text-sm text-gray-500 dark:text-gray-400">
                    No bookings found matching your criteria.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
        {/* Pagination could be added here */}
      </Card>

      {/* Booking Form Modal */}
      <BookingForm
        booking={selectedBooking}
        onSave={handleSaveBooking}
        onCancel={handleCancelForm}
        isOpen={showForm}
      />

      {/* Booking Details Modal */}
      {selectedBooking && (
        <BookingDetails
          booking={selectedBooking}
          onClose={handleCloseDetails}
          onEdit={handleEditFromDetails}
          onStatusUpdate={handleStatusUpdate}
          isOpen={showDetails}
        />
      )}
    </div>
  );
};

export default BookingsPage;
