import React, { SVGProps } from 'react';

export enum UserRole {
  Admin = 'Admin',
  FrontDesk = 'Front Desk',
  Housekeeping = 'Housekeeping',
  Manager = 'Manager',
  Guest = 'Guest', // For potential future portal
}

export enum RoomStatus {
  Available = 'Available',
  Occupied = 'Occupied',
  Cleaning = 'Cleaning',
  OutOfService = 'Out of Service',
  PendingArrival = 'Pending Arrival',
  PendingDeparture = 'Pending Departure',
}

export enum BookingStatus {
  Confirmed = 'Confirmed',
  Pending = 'Pending Payment',
  Cancelled = 'Cancelled',
  CheckedIn = 'Checked In',
  CheckedOut = 'Checked Out',
}

export interface RoomType {
  id: string;
  name: string; // e.g., Single, Double, Suite
  basePrice: number;
  amenities: string[];
  capacity: number;
}

export interface Room {
  id: string;
  roomNumber: string;
  type: RoomType;
  status: RoomStatus;
  currentBookingId?: string;
  notes?: string;
  floor: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface Guest {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address?: string;
  preferences?: string[];
  pastBookings?: string[]; // Array of booking IDs
  createdAt: Date;
  updatedAt: Date;
}

export interface Booking {
  id: string;
  guest: Guest;
  room: Room;
  checkInDate: Date;
  checkOutDate: Date;
  numberOfGuests: number;
  totalAmount: number;
  status: BookingStatus;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface HousekeepingTask {
  id: string;
  roomId: string;
  roomNumber: string;
  taskType: 'Clean' | 'Maintenance' | 'Inspect';
  assignedTo?: string; // Staff ID
  status: 'Pending' | 'In Progress' | 'Completed' | 'Blocked';
  priority: 'Low' | 'Medium' | 'High';
  description?: string;
  reportedAt: Date;
  completedAt?: Date;
}

export interface ReportDataPoint {
  date: string; // or Date object
  value: number;
}

export interface OccupancyReport {
  daily: ReportDataPoint[];
  weekly: ReportDataPoint[];
  monthly: ReportDataPoint[];
}

export interface RevenueReport {
  byRoomType: { typeName: string; revenue: number }[];
  byTimePeriod: ReportDataPoint[];
}

export interface StaffMember {
  id: string;
  name: string;
  role: UserRole;
  email: string;
}

// For navigation items
export interface NavItem {
  path: string;
  name: string;
  icon: (props: SVGProps<SVGSVGElement>) => React.JSX.Element;
  allowedRoles: UserRole[];
}

export type Theme = 'light' | 'dark';

export interface ThemeContextType {
  theme: Theme;
  toggleTheme: () => void;
}

// Form interfaces for creating/editing entities
export interface GuestFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address?: string;
  preferences?: string[];
}

export interface RoomFormData {
  roomNumber: string;
  typeId: string;
  floor: number;
  notes?: string;
}

export interface BookingFormData {
  guestId: string;
  roomId: string;
  checkInDate: string; // ISO date string for form inputs
  checkOutDate: string; // ISO date string for form inputs
  numberOfGuests: number;
  notes?: string;
}

// Form error interfaces
export interface GuestFormErrors {
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  address?: string;
  preferences?: string;
}

export interface RoomFormErrors {
  roomNumber?: string;
  typeId?: string;
  floor?: string;
  notes?: string;
}

export interface BookingFormErrors {
  guestId?: string;
  roomId?: string;
  checkInDate?: string;
  checkOutDate?: string;
  numberOfGuests?: string;
  notes?: string;
}

// Search and filter interfaces
export interface SearchFilters {
  searchTerm?: string;
  status?: string;
  dateFrom?: string;
  dateTo?: string;
}

// Statistics interfaces
export interface DashboardStats {
  totalGuests: number;
  totalRooms: number;
  totalBookings: number;
  occupancyRate: number;
  revenue: number;
  arrivalsToday: number;
  departuresToday: number;
}