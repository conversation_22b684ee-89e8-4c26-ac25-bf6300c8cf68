
import React from 'react';
import { Booking, BookingStatus } from '../../types';
import Badge from '../shared/Badge';
import Button from '../shared/Button';
import { EyeIcon, EditIcon, TrashIcon } from '../shared/Icon';
import { BOOKING_STATUS_COLORS } from '../../constants';

interface BookingListItemProps {
  booking: Booking;
  onViewDetails: (bookingId: string) => void;
  onEdit: (bookingId: string) => void;
  onCancel: (bookingId: string) => void;
}

const BookingListItem: React.FC<BookingListItemProps> = ({ booking, onViewDetails, onEdit, onCancel }) => {
  const formatDate = (date: Date) => new Date(date).toLocaleDateString();

  return (
    <tr className="bg-white dark:bg-navy hover:bg-light-gray dark:hover:bg-navy-light transition-colors duration-150">
      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-dark-gray dark:text-light-gray">{booking.id.substring(0, 6)}...</td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-dark-gray dark:text-light-gray">{`${booking.guest.firstName} ${booking.guest.lastName}`}</td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-dark-gray dark:text-light-gray">{booking.room.roomNumber} ({booking.room.type.name})</td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-dark-gray dark:text-light-gray">{formatDate(booking.checkInDate)}</td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-dark-gray dark:text-light-gray">{formatDate(booking.checkOutDate)}</td>
      <td className="px-6 py-4 whitespace-nowrap text-sm">
        <Badge colorClass={BOOKING_STATUS_COLORS[booking.status] || 'bg-gray-200 text-gray-800'}>
          {booking.status}
        </Badge>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-dark-gray dark:text-light-gray text-right">${booking.totalAmount.toFixed(2)}</td>
      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
        <div className="flex items-center justify-end space-x-2">
          <Button variant="ghost" size="sm" onClick={() => onViewDetails(booking.id)} title="View Details">
            <EyeIcon className="w-4 h-4 text-blue-500" />
          </Button>
          {booking.status !== BookingStatus.Cancelled && booking.status !== BookingStatus.CheckedOut && (
            <>
              <Button variant="ghost" size="sm" onClick={() => onEdit(booking.id)} title="Edit Booking">
                <EditIcon className="w-4 h-4 text-yellow-500" />
              </Button>
              <Button variant="ghost" size="sm" onClick={() => onCancel(booking.id)} title="Cancel Booking">
                <TrashIcon className="w-4 h-4 text-status-red" />
              </Button>
            </>
          )}
        </div>
      </td>
    </tr>
  );
};

export default BookingListItem;
