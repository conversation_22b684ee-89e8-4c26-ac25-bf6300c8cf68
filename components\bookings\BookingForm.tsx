import React, { useState, useEffect } from 'react';
import { Booking, Guest, Room, BookingFormData, BookingFormErrors, BookingStatus, RoomStatus } from '../../types';
import { bookingService } from '../../services/bookingService';
import { guestService } from '../../services/guestService';
import { roomService } from '../../services/roomService';
import Button from '../shared/Button';
import Input from '../shared/Input';
import Select from '../shared/Select';
import Card from '../shared/Card';
import { SaveIcon, XIcon } from '../shared/Icon';

interface BookingFormProps {
  booking?: Booking | undefined;
  onSave: (booking: Booking) => void;
  onCancel: () => void;
  isOpen: boolean;
}

const BookingForm: React.FC<BookingFormProps> = ({
  booking,
  onSave,
  onCancel,
  isOpen
}) => {
  const [formData, setFormData] = useState<BookingFormData>({
    guestId: '',
    roomId: '',
    checkInDate: '',
    checkOutDate: '',
    numberOfGuests: 1,
    notes: ''
  });
  const [guests, setGuests] = useState<Guest[]>([]);
  const [rooms, setRooms] = useState<Room[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingData, setLoadingData] = useState(true);
  const [errors, setErrors] = useState<BookingFormErrors>({});
  const [totalAmount, setTotalAmount] = useState(0);

  useEffect(() => {
    if (isOpen) {
      loadData();
    }
  }, [isOpen]);

  useEffect(() => {
    if (booking) {
      setFormData({
        guestId: booking.guest.id,
        roomId: booking.room.id,
        checkInDate: booking.checkInDate.toISOString().split('T')[0],
        checkOutDate: booking.checkOutDate.toISOString().split('T')[0],
        numberOfGuests: booking.numberOfGuests,
        notes: booking.notes || ''
      });
    } else {
      setFormData({
        guestId: '',
        roomId: '',
        checkInDate: '',
        checkOutDate: '',
        numberOfGuests: 1,
        notes: ''
      });
    }
    setErrors({});
  }, [booking, isOpen]);

  useEffect(() => {
    calculateTotalAmount();
  }, [formData.roomId, formData.checkInDate, formData.checkOutDate]);

  const loadData = async () => {
    try {
      setLoadingData(true);
      const [guestsData, roomsData] = await Promise.all([
        guestService.getAllGuests(),
        roomService.getAllRooms()
      ]);
      
      setGuests(guestsData);
      // For new bookings, only show available rooms
      // For editing, show current room plus available rooms
      if (booking) {
        setRooms(roomsData);
      } else {
        setRooms(roomsData.filter(room => room.status === RoomStatus.Available));
      }
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoadingData(false);
    }
  };

  const calculateTotalAmount = () => {
    if (!formData.roomId || !formData.checkInDate || !formData.checkOutDate) {
      setTotalAmount(0);
      return;
    }

    const selectedRoom = rooms.find(room => room.id === formData.roomId);
    if (!selectedRoom) {
      setTotalAmount(0);
      return;
    }

    const checkIn = new Date(formData.checkInDate);
    const checkOut = new Date(formData.checkOutDate);
    const nights = Math.ceil((checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60 * 24));
    
    if (nights > 0) {
      setTotalAmount(nights * selectedRoom.type.basePrice);
    } else {
      setTotalAmount(0);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: BookingFormErrors = {};

    if (!formData.guestId) {
      newErrors.guestId = 'Guest is required';
    }

    if (!formData.roomId) {
      newErrors.roomId = 'Room is required';
    }

    if (!formData.checkInDate) {
      newErrors.checkInDate = 'Check-in date is required';
    }

    if (!formData.checkOutDate) {
      newErrors.checkOutDate = 'Check-out date is required';
    }

    if (formData.checkInDate && formData.checkOutDate) {
      const checkIn = new Date(formData.checkInDate);
      const checkOut = new Date(formData.checkOutDate);
      
      if (checkOut <= checkIn) {
        newErrors.checkOutDate = 'Check-out date must be after check-in date';
      }

      // For new bookings, check if check-in is not in the past
      if (!booking && checkIn < new Date()) {
        newErrors.checkInDate = 'Check-in date cannot be in the past';
      }
    }

    if (formData.numberOfGuests < 1) {
      newErrors.numberOfGuests = 'Number of guests must be at least 1';
    }

    // Check room capacity
    if (formData.roomId && formData.numberOfGuests) {
      const selectedRoom = rooms.find(room => room.id === formData.roomId);
      if (selectedRoom && formData.numberOfGuests > selectedRoom.type.capacity) {
        newErrors.numberOfGuests = `Room capacity is ${selectedRoom.type.capacity} guests`;
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      const selectedGuest = guests.find(guest => guest.id === formData.guestId);
      const selectedRoom = rooms.find(room => room.id === formData.roomId);

      if (!selectedGuest || !selectedRoom) {
        throw new Error('Selected guest or room not found');
      }

      const bookingData: Booking = {
        id: booking?.id || `booking-${Date.now()}`,
        guest: selectedGuest,
        room: selectedRoom,
        checkInDate: new Date(formData.checkInDate),
        checkOutDate: new Date(formData.checkOutDate),
        numberOfGuests: formData.numberOfGuests,
        totalAmount: totalAmount,
        status: booking?.status || BookingStatus.Confirmed,
        ...(formData.notes?.trim() && { notes: formData.notes.trim() }),
        createdAt: booking?.createdAt || new Date(),
        updatedAt: new Date()
      };

      if (booking) {
        await bookingService.updateBooking(bookingData);
      } else {
        await bookingService.addBooking(bookingData);
      }

      onSave(bookingData);
    } catch (error) {
      console.error('Error saving booking:', error);
      alert('Failed to save booking. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof BookingFormData, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: undefined
      }));
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  if (!isOpen) return null;

  const guestOptions = guests.map(guest => ({
    value: guest.id,
    label: `${guest.firstName} ${guest.lastName} (${guest.email})`
  }));

  const roomOptions = rooms.map(room => ({
    value: room.id,
    label: `Room ${room.roomNumber} - ${room.type.name} (${formatCurrency(room.type.basePrice)}/night)`
  }));

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-navy rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <Card>
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-poppins font-semibold text-navy-dark dark:text-gold-accent">
              {booking ? 'Edit Booking' : 'Create New Booking'}
            </h2>
            <Button
              variant="secondary"
              onClick={onCancel}
              leftIcon={<XIcon className="w-4 h-4" />}
            >
              Close
            </Button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Select
                  label="Guest"
                  options={guestOptions}
                  value={formData.guestId}
                  onChange={(e) => handleInputChange('guestId', e.target.value)}
                  error={errors.guestId}
                  disabled={loadingData}
                  required
                />
                {!loadingData && guests.length === 0 && (
                  <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    No guests found. Please add a guest first.
                  </p>
                )}
              </div>
              <div>
                <Select
                  label="Room"
                  options={roomOptions}
                  value={formData.roomId}
                  onChange={(e) => handleInputChange('roomId', e.target.value)}
                  error={errors.roomId}
                  disabled={loadingData}
                  required
                />
                {!loadingData && rooms.length === 0 && (
                  <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    No available rooms found.
                  </p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="Check-in Date"
                type="date"
                value={formData.checkInDate}
                onChange={(e) => handleInputChange('checkInDate', e.target.value)}
                error={errors.checkInDate}
                required
              />
              <Input
                label="Check-out Date"
                type="date"
                value={formData.checkOutDate}
                onChange={(e) => handleInputChange('checkOutDate', e.target.value)}
                error={errors.checkOutDate}
                required
              />
            </div>

            <Input
              label="Number of Guests"
              type="number"
              min="1"
              value={formData.numberOfGuests.toString()}
              onChange={(e) => handleInputChange('numberOfGuests', parseInt(e.target.value) || 1)}
              error={errors.numberOfGuests}
              required
            />

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Notes
              </label>
              <textarea
                value={formData.notes || ''}
                onChange={(e) => handleInputChange('notes', e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 dark:border-navy-light rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gold-accent focus:border-transparent bg-white dark:bg-navy-light text-gray-900 dark:text-gray-100"
                placeholder="Optional notes for this booking..."
              />
            </div>

            {totalAmount > 0 && (
              <div className="bg-gray-50 dark:bg-navy-light p-4 rounded-lg">
                <div className="flex justify-between items-center">
                  <span className="text-lg font-medium text-gray-900 dark:text-gray-100">
                    Total Amount:
                  </span>
                  <span className="text-xl font-bold text-navy-dark dark:text-gold-accent">
                    {formatCurrency(totalAmount)}
                  </span>
                </div>
                {formData.checkInDate && formData.checkOutDate && (
                  <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    {Math.ceil((new Date(formData.checkOutDate).getTime() - new Date(formData.checkInDate).getTime()) / (1000 * 60 * 60 * 24))} night(s)
                  </p>
                )}
              </div>
            )}

            <div className="flex justify-end space-x-3 pt-4">
              <Button
                type="button"
                variant="secondary"
                onClick={onCancel}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="primary"
                leftIcon={<SaveIcon className="w-4 h-4" />}
                disabled={loading || loadingData || totalAmount === 0}
              >
                {loading ? 'Saving...' : booking ? 'Update Booking' : 'Create Booking'}
              </Button>
            </div>
          </form>
        </Card>
      </div>
    </div>
  );
};

export default BookingForm;
