
import React from 'react';
import { useLocation } from 'react-router-dom';
import ThemeToggle from '../shared/ThemeToggle';
import { UserRole } from '../../types';
import Select from '../shared/Select';
import { USER_ROLES_OPTIONS, NAVIGATION_ITEMS } from '../../constants';
import { MenuIcon, XIcon, ChevronDownIcon } from '../shared/Icon'; // ChevronDown for role selector

interface HeaderProps {
  toggleSidebar: () => void;
  isSidebarOpen: boolean;
  userRole: UserRole;
  setUserRole: (role: UserRole) => void;
}

const Header: React.FC<HeaderProps> = ({ toggleSidebar, isSidebarOpen, userRole, setUserRole }) => {
  const location = useLocation();
  
  const getPageTitle = () => {
    const currentNavItem = NAVIGATION_ITEMS.find(item => item.path === location.pathname);
    return currentNavItem ? currentNavItem.name : 'Dashboard';
  };

  return (
    <header className="flex items-center justify-between h-20 px-6 bg-white dark:bg-navy shadow-md sticky top-0 z-20">
      <div className="flex items-center">
        <button
          onClick={toggleSidebar}
          className="text-dark-gray dark:text-light-gray hover:text-gold-accent dark:hover:text-gold-accent focus:outline-none md:hidden mr-4"
          aria-label={isSidebarOpen ? "Close sidebar" : "Open sidebar"}
        >
          {isSidebarOpen ? <XIcon className="w-6 h-6" /> : <MenuIcon className="w-6 h-6" />}
        </button>
         <button
          onClick={toggleSidebar}
          className="text-dark-gray dark:text-light-gray hover:text-gold-accent dark:hover:text-gold-accent focus:outline-none hidden md:block mr-4"
          aria-label={isSidebarOpen ? "Collapse sidebar" : "Expand sidebar"}
        >
          <MenuIcon className="w-6 h-6" />
        </button>
        <h1 className="text-2xl font-poppins font-semibold text-navy-dark dark:text-gold-accent">{getPageTitle()}</h1>
      </div>

      <div className="flex items-center space-x-4">
        <div className="w-48">
          <Select
            id="userRoleSelect"
            options={USER_ROLES_OPTIONS}
            value={userRole}
            onChange={(e) => setUserRole(e.target.value as UserRole)}
            className="text-sm bg-light-gray dark:bg-navy-light border-medium-gray dark:border-navy rounded-lg"
          />
        </div>
        <ThemeToggle />
        <div className="relative">
          <button className="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-navy-light transition-colors">
            <img
              className="w-8 h-8 rounded-full object-cover"
              src={`https://picsum.photos/seed/${userRole}/40/40`}
              alt="User Avatar"
            />
            <span className="hidden md:inline text-sm font-medium text-dark-gray dark:text-light-gray">{userRole}</span>
            <ChevronDownIcon className="w-4 h-4 text-gray-500 dark:text-gray-400 hidden md:inline" />
          </button>
          {/* Dropdown for user actions - can be implemented later */}
        </div>
      </div>
    </header>
  );
};

export default Header;
