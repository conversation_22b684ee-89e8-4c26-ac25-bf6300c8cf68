import { Guest } from '../types';
import { db, STORES, DatabaseResult, DatabaseError } from '../lib/database';

/**
 * Enhanced Guest Service with comprehensive error handling and validation
 */
export class GuestService {
  /**
   * Add a new guest with validation and error handling
   */
  async addGuest(guest: Guest): Promise<DatabaseResult<Guest>> {
    try {
      // Validate email uniqueness
      const existingGuestResult = await this.getGuestByEmail(guest.email);
      if (existingGuestResult.success && existingGuestResult.data) {
        return { success: false, error: 'A guest with this email already exists' };
      }

      const guestWithTimestamps = {
        ...guest,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const result = await db.add(STORES.GUESTS, guestWithTimestamps);
      if (result.success) {
        return { success: true, data: guestWithTimestamps };
      }
      return result;
    } catch (error) {
      return {
        success: false,
        error: error instanceof DatabaseError ? error.message : 'Failed to add guest'
      };
    }
  }

  /**
   * Update an existing guest with validation
   */
  async updateGuest(guest: Guest): Promise<DatabaseResult<Guest>> {
    try {
      // Check if guest exists
      const existingResult = await this.getGuest(guest.id);
      if (!existingResult.success || !existingResult.data) {
        return { success: false, error: 'Guest not found' };
      }

      // Validate email uniqueness (excluding current guest)
      const emailCheckResult = await this.getGuestByEmail(guest.email);
      if (emailCheckResult.success && emailCheckResult.data && emailCheckResult.data.id !== guest.id) {
        return { success: false, error: 'Another guest with this email already exists' };
      }

      const updatedGuest = {
        ...guest,
        updatedAt: new Date()
      };

      const result = await db.update(STORES.GUESTS, updatedGuest);
      if (result.success) {
        return { success: true, data: updatedGuest };
      }
      return result;
    } catch (error) {
      return {
        success: false,
        error: error instanceof DatabaseError ? error.message : 'Failed to update guest'
      };
    }
  }

  /**
   * Get a single guest by ID
   */
  async getGuest(id: string): Promise<DatabaseResult<Guest>> {
    try {
      return await db.get<Guest>(STORES.GUESTS, id);
    } catch (error) {
      return {
        success: false,
        error: error instanceof DatabaseError ? error.message : 'Failed to get guest'
      };
    }
  }

  /**
   * Get all guests with error handling
   */
  async getAllGuests(): Promise<DatabaseResult<Guest[]>> {
    try {
      return await db.getAll<Guest>(STORES.GUESTS);
    } catch (error) {
      return {
        success: false,
        error: error instanceof DatabaseError ? error.message : 'Failed to get guests'
      };
    }
  }

  /**
   * Delete a guest with validation
   */
  async deleteGuest(id: string): Promise<DatabaseResult<void>> {
    try {
      // Check if guest exists
      const guestResult = await this.getGuest(id);
      if (!guestResult.success || !guestResult.data) {
        return { success: false, error: 'Guest not found' };
      }

      // TODO: Check if guest has active bookings before deletion
      // This would require integration with booking service

      return await db.delete(STORES.GUESTS, id);
    } catch (error) {
      return {
        success: false,
        error: error instanceof DatabaseError ? error.message : 'Failed to delete guest'
      };
    }
  }

  /**
   * Get guest by email address
   */
  async getGuestByEmail(email: string): Promise<DatabaseResult<Guest>> {
    try {
      const result = await db.getByIndex<Guest>(STORES.GUESTS, 'email', email);
      if (result.success && result.data && result.data.length > 0) {
        return { success: true, data: result.data[0] };
      }
      return { success: false, error: 'Guest not found' };
    } catch (error) {
      return {
        success: false,
        error: error instanceof DatabaseError ? error.message : 'Failed to get guest by email'
      };
    }
  }

  /**
   * Search guests by name or email
   */
  async searchGuests(searchTerm: string): Promise<DatabaseResult<Guest[]>> {
    try {
      const allGuestsResult = await this.getAllGuests();
      if (!allGuestsResult.success || !allGuestsResult.data) {
        return { success: false, error: 'Failed to search guests' };
      }

      const searchLower = searchTerm.toLowerCase();
      const filteredGuests = allGuestsResult.data.filter(guest =>
        guest.firstName.toLowerCase().includes(searchLower) ||
        guest.lastName.toLowerCase().includes(searchLower) ||
        guest.email.toLowerCase().includes(searchLower) ||
        `${guest.firstName} ${guest.lastName}`.toLowerCase().includes(searchLower)
      );

      return { success: true, data: filteredGuests };
    } catch (error) {
      return {
        success: false,
        error: error instanceof DatabaseError ? error.message : 'Failed to search guests'
      };
    }
  }
    await db.delete(STORES.GUESTS, id);
  }

  async getGuestByEmail(email: string): Promise<Guest | undefined> {
    const guests = await db.getByIndex<Guest>(STORES.GUESTS, 'email', email);
    return guests[0];
  }

  async getGuestsByLastName(lastName: string): Promise<Guest[]> {
    return await db.getByIndex<Guest>(STORES.GUESTS, 'lastName', lastName);
  }

  async searchGuests(searchTerm: string): Promise<Guest[]> {
    const allGuests = await this.getAllGuests();
    const term = searchTerm.toLowerCase();
    
    return allGuests.filter(guest => 
      guest.firstName.toLowerCase().includes(term) ||
      guest.lastName.toLowerCase().includes(term) ||
      guest.email.toLowerCase().includes(term) ||
      guest.phone.includes(term)
    );
  }

  async getGuestStats(): Promise<{
    total: number;
    withPreferences: number;
    withPastBookings: number;
  }> {
    const allGuests = await this.getAllGuests();
    
    return {
      total: allGuests.length,
      withPreferences: allGuests.filter(g => g.preferences && g.preferences.length > 0).length,
      withPastBookings: allGuests.filter(g => g.pastBookings && g.pastBookings.length > 0).length
    };
  }

  async addPreferenceToGuest(guestId: string, preference: string): Promise<void> {
    const guest = await this.getGuest(guestId);
    if (guest) {
      if (!guest.preferences) {
        guest.preferences = [];
      }
      if (!guest.preferences.includes(preference)) {
        guest.preferences.push(preference);
        await this.updateGuest(guest);
      }
    }
  }

  async removePreferenceFromGuest(guestId: string, preference: string): Promise<void> {
    const guest = await this.getGuest(guestId);
    if (guest && guest.preferences) {
      guest.preferences = guest.preferences.filter(p => p !== preference);
      await this.updateGuest(guest);
    }
  }

  async addBookingToGuestHistory(guestId: string, bookingId: string): Promise<void> {
    const guest = await this.getGuest(guestId);
    if (guest) {
      if (!guest.pastBookings) {
        guest.pastBookings = [];
      }
      if (!guest.pastBookings.includes(bookingId)) {
        guest.pastBookings.push(bookingId);
        await this.updateGuest(guest);
      }
    }
  }
}

export const guestService = new GuestService();
