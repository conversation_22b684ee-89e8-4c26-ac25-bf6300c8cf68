import { BaseEntity } from '../types';

/**
 * Database configuration constants
 */
const DB_NAME = 'HotelManagementDB';
const DB_VERSION = 1;

/**
 * IndexedDB store names
 * @description Centralized store name definitions for type safety
 */
export const STORES = {
  ROOM_TYPES: 'roomTypes',
  ROOMS: 'rooms',
  GUESTS: 'guests',
  BOOKINGS: 'bookings',
  HOUSEKEEPING_TASKS: 'housekeepingTasks',
  STAFF: 'staff',
  SETTINGS: 'settings'
} as const;

export type StoreName = typeof STORES[keyof typeof STORES];

/**
 * Database operation result types
 */
export interface DatabaseResult<T> {
  success: boolean;
  data?: T;
  error?: string;
}

/**
 * Database error types
 */
export class DatabaseError extends Error {
  constructor(
    message: string,
    public readonly operation: string,
    public readonly store?: string,
    public readonly originalError?: Error
  ) {
    super(message);
    this.name = 'DatabaseError';
  }
}

/**
 * Transaction options
 */
export interface TransactionOptions {
  mode?: 'readonly' | 'readwrite';
  durability?: 'default' | 'strict' | 'relaxed';
}

/**
 * Enhanced DatabaseManager with modern patterns and error handling
 * @description Manages IndexedDB operations with type safety and performance optimizations
 */
class DatabaseManager {
  private db: IDBDatabase | null = null;
  private initPromise: Promise<void> | null = null;

  /**
   * Initialize the database connection
   * @returns Promise that resolves when database is ready
   */
  async init(): Promise<void> {
    // Prevent multiple initialization attempts
    if (this.initPromise) {
      return this.initPromise;
    }

    this.initPromise = new Promise((resolve, reject) => {
      try {
        const request = indexedDB.open(DB_NAME, DB_VERSION);

        request.onerror = () => {
          const error = new DatabaseError(
            'Failed to open database',
            'init',
            undefined,
            request.error || undefined
          );
          reject(error);
        };

        request.onsuccess = () => {
          this.db = request.result;
          this.setupErrorHandlers();
          resolve();
        };

        request.onupgradeneeded = (event) => {
          try {
            const db = (event.target as IDBOpenDBRequest).result;
            this.createStores(db);
          } catch (error) {
            reject(new DatabaseError(
              'Failed to upgrade database schema',
              'upgrade',
              undefined,
              error instanceof Error ? error : new Error(String(error))
            ));
          }
        };
      } catch (error) {
        reject(new DatabaseError(
          'Failed to initialize database',
          'init',
          undefined,
          error instanceof Error ? error : new Error(String(error))
        ));
      }
    });

    return this.initPromise;
  }

  /**
   * Setup global error handlers for the database
   */
  private setupErrorHandlers(): void {
    if (this.db) {
      this.db.onerror = (event) => {
        console.error('Database error:', event);
      };

      this.db.onversionchange = () => {
        this.db?.close();
        this.db = null;
        this.initPromise = null;
        console.warn('Database version changed. Please refresh the page.');
      };
    }
  }

  /**
   * Create all required object stores and indexes
   */
  private createStores(db: IDBDatabase): void {
    // Create Room Types store
    if (!db.objectStoreNames.contains(STORES.ROOM_TYPES)) {
      const roomTypesStore = db.createObjectStore(STORES.ROOM_TYPES, { keyPath: 'id' });
      roomTypesStore.createIndex('name', 'name', { unique: true });
    }

    // Create Rooms store
    if (!db.objectStoreNames.contains(STORES.ROOMS)) {
      const roomsStore = db.createObjectStore(STORES.ROOMS, { keyPath: 'id' });
      roomsStore.createIndex('roomNumber', 'roomNumber', { unique: true });
      roomsStore.createIndex('status', 'status', { unique: false });
      roomsStore.createIndex('floor', 'floor', { unique: false });
      roomsStore.createIndex('type.id', 'type.id', { unique: false });
    }

    // Create Guests store
    if (!db.objectStoreNames.contains(STORES.GUESTS)) {
      const guestsStore = db.createObjectStore(STORES.GUESTS, { keyPath: 'id' });
      guestsStore.createIndex('email', 'email', { unique: true });
      guestsStore.createIndex('phone', 'phone', { unique: false });
      guestsStore.createIndex('lastName', 'lastName', { unique: false });
      guestsStore.createIndex('fullName', ['firstName', 'lastName'], { unique: false });
    }

    // Create Bookings store
    if (!db.objectStoreNames.contains(STORES.BOOKINGS)) {
      const bookingsStore = db.createObjectStore(STORES.BOOKINGS, { keyPath: 'id' });
      bookingsStore.createIndex('guestId', 'guest.id', { unique: false });
      bookingsStore.createIndex('roomId', 'room.id', { unique: false });
      bookingsStore.createIndex('status', 'status', { unique: false });
      bookingsStore.createIndex('checkInDate', 'checkInDate', { unique: false });
      bookingsStore.createIndex('checkOutDate', 'checkOutDate', { unique: false });
      bookingsStore.createIndex('dateRange', ['checkInDate', 'checkOutDate'], { unique: false });
    }

    // Create Housekeeping Tasks store
    if (!db.objectStoreNames.contains(STORES.HOUSEKEEPING_TASKS)) {
      const tasksStore = db.createObjectStore(STORES.HOUSEKEEPING_TASKS, { keyPath: 'id' });
      tasksStore.createIndex('roomId', 'roomId', { unique: false });
      tasksStore.createIndex('status', 'status', { unique: false });
      tasksStore.createIndex('priority', 'priority', { unique: false });
      tasksStore.createIndex('assignedTo', 'assignedTo', { unique: false });
      tasksStore.createIndex('taskType', 'taskType', { unique: false });
      tasksStore.createIndex('reportedAt', 'reportedAt', { unique: false });
    }

    // Create Staff store
    if (!db.objectStoreNames.contains(STORES.STAFF)) {
      const staffStore = db.createObjectStore(STORES.STAFF, { keyPath: 'id' });
      staffStore.createIndex('email', 'email', { unique: true });
      staffStore.createIndex('role', 'role', { unique: false });
    }

    // Create Settings store
    if (!db.objectStoreNames.contains(STORES.SETTINGS)) {
      db.createObjectStore(STORES.SETTINGS, { keyPath: 'key' });
    }
  }

  /**
   * Get an object store with enhanced error handling
   */
  private async getStore(
    storeName: StoreName,
    mode: 'readonly' | 'readwrite' = 'readonly',
    options?: TransactionOptions
  ): Promise<IDBObjectStore> {
    await this.ensureInitialized();

    if (!this.db) {
      throw new DatabaseError('Database not initialized', 'getStore', storeName);
    }

    try {
      const transaction = this.db.transaction([storeName], mode, options?.durability);
      const store = transaction.objectStore(storeName);

      // Add transaction error handling
      transaction.onerror = () => {
        throw new DatabaseError(
          `Transaction failed for store: ${storeName}`,
          'transaction',
          storeName,
          transaction.error || undefined
        );
      };

      return store;
    } catch (error) {
      throw new DatabaseError(
        `Failed to get store: ${storeName}`,
        'getStore',
        storeName,
        error instanceof Error ? error : new Error(String(error))
      );
    }
  }

  /**
   * Ensure database is initialized
   */
  private async ensureInitialized(): Promise<void> {
    if (!this.db && !this.initPromise) {
      await this.init();
    } else if (this.initPromise) {
      await this.initPromise;
    }
  }

  /**
   * Enhanced add operation with type safety and error handling
   */
  async add<T extends BaseEntity>(storeName: StoreName, data: T): Promise<DatabaseResult<T>> {
    try {
      const store = await this.getStore(storeName, 'readwrite');

      return new Promise((resolve, reject) => {
        const request = store.add(data);

        request.onsuccess = () => {
          resolve({ success: true, data });
        };

        request.onerror = () => {
          const error = new DatabaseError(
            `Failed to add record to ${storeName}`,
            'add',
            storeName,
            request.error || undefined
          );
          resolve({ success: false, error: error.message });
        };
      });
    } catch (error) {
      return {
        success: false,
        error: error instanceof DatabaseError ? error.message : String(error)
      };
    }
  }

  async update<T>(storeName: string, data: T): Promise<void> {
    return new Promise((resolve, reject) => {
      const store = this.getStore(storeName, 'readwrite');
      const request = store.put(data);
      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  async get<T>(storeName: string, id: string): Promise<T | undefined> {
    return new Promise((resolve, reject) => {
      const store = this.getStore(storeName);
      const request = store.get(id);
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  async getAll<T>(storeName: string): Promise<T[]> {
    return new Promise((resolve, reject) => {
      const store = this.getStore(storeName);
      const request = store.getAll();
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  async delete(storeName: string, id: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const store = this.getStore(storeName, 'readwrite');
      const request = store.delete(id);
      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  async clear(storeName: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const store = this.getStore(storeName, 'readwrite');
      const request = store.clear();
      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  async getByIndex<T>(storeName: string, indexName: string, value: string | number): Promise<T[]> {
    return new Promise((resolve, reject) => {
      const store = this.getStore(storeName);
      const index = store.index(indexName);
      const request = index.getAll(value);
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }
}

// Singleton instance
export const db = new DatabaseManager();

// Initialize database
export const initDatabase = async (): Promise<void> => {
  await db.init();
};
