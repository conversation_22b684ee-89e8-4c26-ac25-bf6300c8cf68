import { Room, RoomStatus } from '../types';
import { db, STORES } from '../lib/database';

export class RoomService {
  async addRoom(room: Room): Promise<void> {
    const roomWithTimestamps = {
      ...room,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    await db.add(STORES.ROOMS, roomWithTimestamps);
  }

  async updateRoom(room: Room): Promise<void> {
    const updatedRoom = {
      ...room,
      updatedAt: new Date()
    };
    await db.update(STORES.ROOMS, updatedRoom);
  }

  async getRoom(id: string): Promise<Room | undefined> {
    return await db.get<Room>(STORES.ROOMS, id);
  }

  async getAllRooms(): Promise<Room[]> {
    return await db.getAll<Room>(STORES.ROOMS);
  }

  async deleteRoom(id: string): Promise<void> {
    await db.delete(STORES.ROOMS, id);
  }

  async getRoomsByStatus(status: RoomStatus): Promise<Room[]> {
    return await db.getByIndex<Room>(STORES.ROOMS, 'status', status);
  }

  async getRoomsByFloor(floor: number): Promise<Room[]> {
    return await db.getByIndex<Room>(STORES.ROOMS, 'floor', floor);
  }

  async getRoomByNumber(roomNumber: string): Promise<Room | undefined> {
    const rooms = await db.getByIndex<Room>(STORES.ROOMS, 'roomNumber', roomNumber);
    return rooms[0];
  }

  async updateRoomStatus(roomId: string, status: RoomStatus, currentBookingId?: string): Promise<void> {
    const room = await this.getRoom(roomId);
    if (room) {
      room.status = status;
      if (currentBookingId !== undefined) {
        room.currentBookingId = currentBookingId;
      }
      await this.updateRoom(room);
    }
  }

  async getAvailableRooms(): Promise<Room[]> {
    return await this.getRoomsByStatus(RoomStatus.Available);
  }

  async getOccupiedRooms(): Promise<Room[]> {
    return await this.getRoomsByStatus(RoomStatus.Occupied);
  }

  async getRoomsNeedingCleaning(): Promise<Room[]> {
    return await this.getRoomsByStatus(RoomStatus.Cleaning);
  }

  async getOutOfServiceRooms(): Promise<Room[]> {
    return await this.getRoomsByStatus(RoomStatus.OutOfService);
  }

  async getRoomStats(): Promise<{
    total: number;
    available: number;
    occupied: number;
    cleaning: number;
    outOfService: number;
    occupancyRate: number;
  }> {
    const allRooms = await this.getAllRooms();
    const available = allRooms.filter(r => r.status === RoomStatus.Available).length;
    const occupied = allRooms.filter(r => r.status === RoomStatus.Occupied).length;
    const cleaning = allRooms.filter(r => r.status === RoomStatus.Cleaning).length;
    const outOfService = allRooms.filter(r => r.status === RoomStatus.OutOfService).length;
    
    return {
      total: allRooms.length,
      available,
      occupied,
      cleaning,
      outOfService,
      occupancyRate: allRooms.length > 0 ? (occupied / allRooms.length) * 100 : 0
    };
  }
}

export const roomService = new RoomService();
