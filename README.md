# Premium Stay Hotel Management System

A comprehensive hotel management system built with React, TypeScript, and modern web technologies. This project uses IndexedDB for local data storage, making it a fully offline-capable application perfect for portfolio demonstration.

## Features

- **Dashboard**: Real-time overview of hotel operations with dynamic statistics
- **Room Management**: Track room status, availability, and maintenance
- **Booking System**: Manage reservations and guest information
- **Guest Management**: Maintain guest profiles and preferences
- **Housekeeping**: Task management and room cleaning schedules
- **Reports**: Analytics and insights for business decisions
- **Dark/Light Theme**: Toggle between themes for better user experience
- **Offline Capability**: Works completely offline using IndexedDB
- **No Mock Data**: All data is real and stored locally

## Technology Stack

- **Frontend**: React 18.3.1, TypeScript 5.7.2
- **Routing**: React Router DOM 7.6.2
- **Charts**: Recharts 2.12.7 for data visualization
- **Build Tool**: Vite 6.2.0
- **Styling**: Tailwind CSS 3.4.17 with custom design system
- **UI Components**: Radix <PERSON> primitives for accessibility
- **Form Handling**: React Hook Form with Zod validation
- **Database**: IndexedDB for local data storage
- **Icons**: Lucide React for consistent iconography
- **Development**: ESLint, Prettier, TypeScript for code quality

## Getting Started

### Prerequisites

- Node.js (version 18 or higher)
- npm (version 9 or higher)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd hotel-management
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:3000`

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint issues
- `npm run type-check` - Run TypeScript type checking

## Project Structure

```
├── components/          # Reusable UI components
│   ├── layout/         # Layout components (Header, Sidebar)
│   ├── shared/         # Shared components (Button, Card, etc.)
│   ├── dashboard/      # Dashboard-specific components
│   ├── bookings/       # Booking-related components
│   ├── rooms/          # Room management components
│   └── housekeeping/   # Housekeeping task components
├── pages/              # Page components
├── services/           # Business logic and data services
│   ├── roomService.ts      # Room and room type management
│   ├── guestService.ts     # Guest management
│   ├── bookingService.ts   # Booking management
│   ├── housekeepingService.ts # Housekeeping task management
│   └── dataInitService.ts  # Application initialization
├── lib/                # Utility functions and database
│   ├── database.ts     # IndexedDB setup and management
│   └── utils.ts        # Helper functions
├── hooks/              # Custom React hooks
├── src/styles/         # Global styles and Tailwind CSS
├── types.ts            # TypeScript type definitions
├── constants.ts        # Application constants
├── App.tsx            # Main application component
├── tailwind.config.js  # Tailwind CSS configuration
├── postcss.config.js   # PostCSS configuration
├── eslint.config.js    # ESLint configuration
└── .prettierrc         # Prettier configuration
```

## Database Schema

The application uses IndexedDB with the following stores:

- **roomTypes**: Room type definitions (Single, Double, Suite, etc.)
- **rooms**: Individual room records with status and details
- **guests**: Guest profiles and preferences
- **bookings**: Reservation records linking guests and rooms
- **housekeepingTasks**: Cleaning and maintenance tasks
- **staff**: Staff member information
- **settings**: Application settings and preferences

## Key Features

### Real-time Data Management
- All data is stored locally using IndexedDB
- No external dependencies or API calls required
- Automatic data persistence across browser sessions

### Room Management
- Track room availability and status
- Manage different room types and pricing
- Handle room maintenance and cleaning schedules

### Booking System
- Create and manage guest reservations
- Check-in and check-out functionality
- Booking status tracking and updates

### Guest Management
- Store guest information and preferences
- Track booking history
- Manage guest communications

### Housekeeping
- Create and assign cleaning tasks
- Track task progress and completion
- Priority-based task management

### Dashboard Analytics
- Real-time occupancy statistics
- Revenue tracking and reporting
- Activity feed for recent events

## Design System

The project uses a modern design system built with Tailwind CSS:

- **Colors**: CSS custom properties for theme switching
- **Typography**: Consistent font scales and weights
- **Spacing**: Standardized spacing system
- **Components**: Reusable UI components with variants
- **Dark Mode**: Full dark mode support
- **Responsive**: Mobile-first responsive design

## Development Guidelines

- Use TypeScript for all new code
- Follow ESLint and Prettier configurations
- Use Tailwind CSS for styling
- Implement proper error boundaries
- Use React Hook Form for form handling
- Validate data with Zod schemas
- Write accessible components using Radix UI

## Portfolio Highlights

This project demonstrates:

- **Modern React Development**: Hooks, Context, TypeScript
- **State Management**: Local state with IndexedDB persistence
- **UI/UX Design**: Professional hotel management interface
- **Data Architecture**: Normalized database design
- **Performance**: Optimized rendering and data loading
- **Accessibility**: WCAG compliant components
- **Code Quality**: ESLint, Prettier, TypeScript strict mode

## License

This project is licensed under the MIT License.
