import React, { useState } from 'react';
import { Booking, BookingStatus } from '../../types';
import { bookingService } from '../../services/bookingService';
import Button from '../shared/Button';
import Card from '../shared/Card';
import Badge from '../shared/Badge';
import { 
  XIcon, 
  EditIcon, 
  CalendarIcon, 
  UsersIcon, 
  DollarSignIcon, 
  CheckIcon, 
  XCircleIcon,
  PhoneIcon,
  MailIcon,
  MapPinIcon
} from '../shared/Icon';

interface BookingDetailsProps {
  booking: Booking;
  onClose: () => void;
  onEdit: () => void;
  onStatusUpdate: (booking: Booking) => void;
  isOpen: boolean;
}

const BookingDetails: React.FC<BookingDetailsProps> = ({
  booking,
  onClose,
  onEdit,
  onStatusUpdate,
  isOpen
}) => {
  const [loading, setLoading] = useState(false);

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const getStatusColor = (status: BookingStatus) => {
    switch (status) {
      case BookingStatus.Confirmed:
        return 'success';
      case BookingStatus.CheckedIn:
        return 'info';
      case BookingStatus.CheckedOut:
        return 'secondary';
      case BookingStatus.Cancelled:
        return 'danger';
      case BookingStatus.Pending:
        return 'warning';
      default:
        return 'secondary';
    }
  };

  const calculateNights = () => {
    const checkIn = new Date(booking.checkInDate);
    const checkOut = new Date(booking.checkOutDate);
    return Math.ceil((checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60 * 24));
  };

  const handleCheckIn = async () => {
    if (booking.status !== BookingStatus.Confirmed) return;
    
    setLoading(true);
    try {
      await bookingService.checkInGuest(booking.id);
      const updatedBooking = { ...booking, status: BookingStatus.CheckedIn };
      onStatusUpdate(updatedBooking);
    } catch (error) {
      console.error('Error checking in guest:', error);
      alert('Failed to check in guest. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleCheckOut = async () => {
    if (booking.status !== BookingStatus.CheckedIn) return;
    
    setLoading(true);
    try {
      await bookingService.checkOutGuest(booking.id);
      const updatedBooking = { ...booking, status: BookingStatus.CheckedOut };
      onStatusUpdate(updatedBooking);
    } catch (error) {
      console.error('Error checking out guest:', error);
      alert('Failed to check out guest. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = async () => {
    if (booking.status === BookingStatus.Cancelled || booking.status === BookingStatus.CheckedOut) return;
    
    if (!window.confirm('Are you sure you want to cancel this booking?')) return;
    
    setLoading(true);
    try {
      await bookingService.cancelBooking(booking.id);
      const updatedBooking = { ...booking, status: BookingStatus.Cancelled };
      onStatusUpdate(updatedBooking);
    } catch (error) {
      console.error('Error cancelling booking:', error);
      alert('Failed to cancel booking. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  const nights = calculateNights();
  const pricePerNight = nights > 0 ? booking.totalAmount / nights : booking.room.type.basePrice;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-navy rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <Card>
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-poppins font-semibold text-navy-dark dark:text-gold-accent">
              Booking Details
            </h2>
            <div className="flex space-x-2">
              <Button
                variant="primary"
                onClick={onEdit}
                leftIcon={<EditIcon className="w-4 h-4" />}
                disabled={loading}
              >
                Edit
              </Button>
              <Button
                variant="secondary"
                onClick={onClose}
                leftIcon={<XIcon className="w-4 h-4" />}
              >
                Close
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Booking Overview */}
            <div className="lg:col-span-2">
              <Card>
                <div className="flex justify-between items-start mb-6">
                  <div>
                    <h3 className="text-xl font-semibold text-navy-dark dark:text-gold-accent">
                      Booking #{booking.id}
                    </h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Created on {formatDate(booking.createdAt)}
                    </p>
                  </div>
                  <Badge variant={getStatusColor(booking.status) as any} size="lg">
                    {booking.status}
                  </Badge>
                </div>

                {/* Guest Information */}
                <div className="mb-6">
                  <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">
                    Guest Information
                  </h4>
                  <div className="bg-gray-50 dark:bg-navy-light rounded-lg p-4">
                    <div className="flex items-center mb-4">
                      <div className="h-12 w-12 rounded-full bg-gold-accent flex items-center justify-center mr-4">
                        <span className="text-lg font-bold text-white">
                          {booking.guest.firstName.charAt(0)}{booking.guest.lastName.charAt(0)}
                        </span>
                      </div>
                      <div>
                        <h5 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                          {booking.guest.firstName} {booking.guest.lastName}
                        </h5>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          Guest ID: {booking.guest.id}
                        </p>
                      </div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="flex items-center space-x-3">
                        <MailIcon className="w-5 h-5 text-gray-400" />
                        <span className="text-sm text-gray-700 dark:text-gray-300">
                          {booking.guest.email}
                        </span>
                      </div>
                      <div className="flex items-center space-x-3">
                        <PhoneIcon className="w-5 h-5 text-gray-400" />
                        <span className="text-sm text-gray-700 dark:text-gray-300">
                          {booking.guest.phone}
                        </span>
                      </div>
                      {booking.guest.address && (
                        <div className="flex items-center space-x-3 md:col-span-2">
                          <MapPinIcon className="w-5 h-5 text-gray-400" />
                          <span className="text-sm text-gray-700 dark:text-gray-300">
                            {booking.guest.address}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Room Information */}
                <div className="mb-6">
                  <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">
                    Room Information
                  </h4>
                  <div className="bg-gray-50 dark:bg-navy-light rounded-lg p-4">
                    <div className="flex items-center mb-4">
                      <div className="h-12 w-12 rounded-lg bg-navy dark:bg-gold-accent flex items-center justify-center mr-4">
                        <span className="text-lg font-bold text-white">
                          {booking.room.roomNumber}
                        </span>
                      </div>
                      <div>
                        <h5 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                          Room {booking.room.roomNumber} - {booking.room.type.name}
                        </h5>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          Floor {booking.room.floor} • Capacity: {booking.room.type.capacity} guests
                        </p>
                      </div>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {booking.room.type.amenities.map((amenity, index) => (
                        <Badge key={index} variant="info">
                          {amenity}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Stay Details */}
                <div className="mb-6">
                  <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">
                    Stay Details
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center space-x-3">
                      <CalendarIcon className="w-5 h-5 text-gray-400" />
                      <div>
                        <p className="text-sm text-gray-500 dark:text-gray-400">Check-in</p>
                        <p className="font-medium text-gray-900 dark:text-gray-100">
                          {formatDate(booking.checkInDate)}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <CalendarIcon className="w-5 h-5 text-gray-400" />
                      <div>
                        <p className="text-sm text-gray-500 dark:text-gray-400">Check-out</p>
                        <p className="font-medium text-gray-900 dark:text-gray-100">
                          {formatDate(booking.checkOutDate)}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <UsersIcon className="w-5 h-5 text-gray-400" />
                      <div>
                        <p className="text-sm text-gray-500 dark:text-gray-400">Guests</p>
                        <p className="font-medium text-gray-900 dark:text-gray-100">
                          {booking.numberOfGuests} guest{booking.numberOfGuests !== 1 ? 's' : ''}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <DollarSignIcon className="w-5 h-5 text-gray-400" />
                      <div>
                        <p className="text-sm text-gray-500 dark:text-gray-400">Duration</p>
                        <p className="font-medium text-gray-900 dark:text-gray-100">
                          {nights} night{nights !== 1 ? 's' : ''}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {booking.notes && (
                  <div>
                    <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">
                      Notes
                    </h4>
                    <div className="bg-gray-50 dark:bg-navy-light rounded-lg p-4">
                      <p className="text-gray-700 dark:text-gray-300">
                        {booking.notes}
                      </p>
                    </div>
                  </div>
                )}
              </Card>
            </div>

            {/* Actions & Summary */}
            <div className="lg:col-span-1">
              <Card>
                <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                  Booking Summary
                </h4>
                
                <div className="space-y-3 mb-6">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Room Rate:</span>
                    <span className="font-medium">{formatCurrency(pricePerNight)}/night</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Nights:</span>
                    <span className="font-medium">{nights}</span>
                  </div>
                  <div className="border-t border-gray-200 dark:border-navy-light pt-3">
                    <div className="flex justify-between">
                      <span className="text-lg font-medium text-gray-900 dark:text-gray-100">Total:</span>
                      <span className="text-lg font-bold text-navy-dark dark:text-gold-accent">
                        {formatCurrency(booking.totalAmount)}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  {booking.status === BookingStatus.Confirmed && (
                    <Button
                      variant="success"
                      onClick={handleCheckIn}
                      leftIcon={<CheckIcon className="w-4 h-4" />}
                      disabled={loading}
                      className="w-full"
                    >
                      Check In Guest
                    </Button>
                  )}
                  
                  {booking.status === BookingStatus.CheckedIn && (
                    <Button
                      variant="primary"
                      onClick={handleCheckOut}
                      leftIcon={<CheckIcon className="w-4 h-4" />}
                      disabled={loading}
                      className="w-full"
                    >
                      Check Out Guest
                    </Button>
                  )}
                  
                  {booking.status !== BookingStatus.Cancelled && booking.status !== BookingStatus.CheckedOut && (
                    <Button
                      variant="danger"
                      onClick={handleCancel}
                      leftIcon={<XCircleIcon className="w-4 h-4" />}
                      disabled={loading}
                      className="w-full"
                    >
                      Cancel Booking
                    </Button>
                  )}
                </div>

                <div className="mt-6 pt-6 border-t border-gray-200 dark:border-navy-light">
                  <div className="text-center">
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      Last updated
                    </div>
                    <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {formatDate(booking.updatedAt)}
                    </div>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default BookingDetails;
