import { useState, useCallback, useMemo } from 'react';

/**
 * Validation rule function type
 */
export type ValidationRule<T> = (value: T) => string | null;

/**
 * Field validation configuration
 */
export interface FieldValidation<T> {
  required?: boolean;
  rules?: ValidationRule<T>[];
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
}

/**
 * Form field state
 */
export interface FieldState<T> {
  value: T;
  error: string | null;
  touched: boolean;
  dirty: boolean;
}

/**
 * Form validation configuration
 */
export type FormValidation<T> = {
  [K in keyof T]: FieldValidation<T[K]>;
};

/**
 * Form state
 */
export interface FormState<T> {
  fields: { [K in keyof T]: FieldState<T[K]> };
  isValid: boolean;
  isSubmitting: boolean;
  submitCount: number;
  errors: { [K in keyof T]?: string };
}

/**
 * Common validation rules
 */
export const ValidationRules = {
  email: (value: string): string | null => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(value) ? null : 'Please enter a valid email address';
  },

  phone: (value: string): string | null => {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(value.replace(/\s/g, '')) ? null : 'Please enter a valid phone number';
  },

  minLength: (min: number) => (value: string): string | null => {
    return value.length >= min ? null : `Must be at least ${min} characters long`;
  },

  maxLength: (max: number) => (value: string): string | null => {
    return value.length <= max ? null : `Must be no more than ${max} characters long`;
  },

  min: (min: number) => (value: number): string | null => {
    return value >= min ? null : `Must be at least ${min}`;
  },

  max: (max: number) => (value: number): string | null => {
    return value <= max ? null : `Must be no more than ${max}`;
  },

  pattern: (pattern: RegExp, message: string) => (value: string): string | null => {
    return pattern.test(value) ? null : message;
  },

  custom: <T>(validator: (value: T) => boolean, message: string) => (value: T): string | null => {
    return validator(value) ? null : message;
  }
};

/**
 * Enhanced form validation hook with comprehensive error handling and validation rules
 * @param initialValues Initial form values
 * @param validationConfig Validation configuration for each field
 * @returns Form state and control functions
 */
export function useFormValidation<T extends Record<string, any>>(
  initialValues: T,
  validationConfig: FormValidation<T>
) {
  const [formState, setFormState] = useState<FormState<T>>(() => {
    const fields = {} as { [K in keyof T]: FieldState<T[K]> };
    
    Object.keys(initialValues).forEach(key => {
      const k = key as keyof T;
      fields[k] = {
        value: initialValues[k],
        error: null,
        touched: false,
        dirty: false
      };
    });

    return {
      fields,
      isValid: true,
      isSubmitting: false,
      submitCount: 0,
      errors: {}
    };
  });

  /**
   * Validate a single field
   */
  const validateField = useCallback(<K extends keyof T>(
    fieldName: K,
    value: T[K]
  ): string | null => {
    const config = validationConfig[fieldName];
    if (!config) return null;

    // Check required validation
    if (config.required && (value === null || value === undefined || value === '')) {
      return 'This field is required';
    }

    // Skip other validations if field is empty and not required
    if (!config.required && (value === null || value === undefined || value === '')) {
      return null;
    }

    // Run custom validation rules
    if (config.rules) {
      for (const rule of config.rules) {
        const error = rule(value);
        if (error) return error;
      }
    }

    return null;
  }, [validationConfig]);

  /**
   * Validate all fields
   */
  const validateForm = useCallback((): boolean => {
    const newErrors: { [K in keyof T]?: string } = {};
    let isValid = true;

    Object.keys(formState.fields).forEach(key => {
      const k = key as keyof T;
      const error = validateField(k, formState.fields[k].value);
      if (error) {
        newErrors[k] = error;
        isValid = false;
      }
    });

    setFormState(prev => ({
      ...prev,
      errors: newErrors,
      isValid
    }));

    return isValid;
  }, [formState.fields, validateField]);

  /**
   * Set field value with optional validation
   */
  const setFieldValue = useCallback(<K extends keyof T>(
    fieldName: K,
    value: T[K],
    shouldValidate = true
  ) => {
    const config = validationConfig[fieldName];
    const shouldValidateOnChange = config?.validateOnChange !== false;

    setFormState(prev => {
      const newFields = { ...prev.fields };
      const currentField = newFields[fieldName];
      
      newFields[fieldName] = {
        ...currentField,
        value,
        dirty: value !== initialValues[fieldName],
        error: shouldValidate && shouldValidateOnChange 
          ? validateField(fieldName, value) 
          : currentField.error
      };

      const newErrors = { ...prev.errors };
      if (shouldValidate && shouldValidateOnChange) {
        const error = validateField(fieldName, value);
        if (error) {
          newErrors[fieldName] = error;
        } else {
          delete newErrors[fieldName];
        }
      }

      return {
        ...prev,
        fields: newFields,
        errors: newErrors,
        isValid: Object.keys(newErrors).length === 0
      };
    });
  }, [validationConfig, validateField, initialValues]);

  /**
   * Set field as touched (usually on blur)
   */
  const setFieldTouched = useCallback(<K extends keyof T>(
    fieldName: K,
    shouldValidate = true
  ) => {
    const config = validationConfig[fieldName];
    const shouldValidateOnBlur = config?.validateOnBlur !== false;

    setFormState(prev => {
      const newFields = { ...prev.fields };
      const currentField = newFields[fieldName];
      
      newFields[fieldName] = {
        ...currentField,
        touched: true,
        error: shouldValidate && shouldValidateOnBlur 
          ? validateField(fieldName, currentField.value) 
          : currentField.error
      };

      const newErrors = { ...prev.errors };
      if (shouldValidate && shouldValidateOnBlur) {
        const error = validateField(fieldName, currentField.value);
        if (error) {
          newErrors[fieldName] = error;
        } else {
          delete newErrors[fieldName];
        }
      }

      return {
        ...prev,
        fields: newFields,
        errors: newErrors,
        isValid: Object.keys(newErrors).length === 0
      };
    });
  }, [validationConfig, validateField]);

  /**
   * Reset form to initial state
   */
  const resetForm = useCallback(() => {
    const fields = {} as { [K in keyof T]: FieldState<T[K]> };
    
    Object.keys(initialValues).forEach(key => {
      const k = key as keyof T;
      fields[k] = {
        value: initialValues[k],
        error: null,
        touched: false,
        dirty: false
      };
    });

    setFormState({
      fields,
      isValid: true,
      isSubmitting: false,
      submitCount: 0,
      errors: {}
    });
  }, [initialValues]);

  /**
   * Set submitting state
   */
  const setSubmitting = useCallback((isSubmitting: boolean) => {
    setFormState(prev => ({
      ...prev,
      isSubmitting,
      submitCount: isSubmitting ? prev.submitCount : prev.submitCount + 1
    }));
  }, []);

  /**
   * Get current form values
   */
  const values = useMemo(() => {
    const result = {} as T;
    Object.keys(formState.fields).forEach(key => {
      const k = key as keyof T;
      result[k] = formState.fields[k].value;
    });
    return result;
  }, [formState.fields]);

  /**
   * Check if form has any dirty fields
   */
  const isDirty = useMemo(() => {
    return Object.values(formState.fields).some(field => field.dirty);
  }, [formState.fields]);

  /**
   * Check if form has any touched fields
   */
  const isTouched = useMemo(() => {
    return Object.values(formState.fields).some(field => field.touched);
  }, [formState.fields]);

  return {
    // State
    fields: formState.fields,
    errors: formState.errors,
    isValid: formState.isValid,
    isSubmitting: formState.isSubmitting,
    submitCount: formState.submitCount,
    values,
    isDirty,
    isTouched,

    // Actions
    setFieldValue,
    setFieldTouched,
    validateForm,
    validateField,
    resetForm,
    setSubmitting
  };
}
