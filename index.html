
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Premium Stay Hotel Management</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        darkMode: 'class', // or 'media'
        theme: {
          fontFamily: {
            sans: ['Inter', 'sans-serif'],
            poppins: ['Poppins', 'sans-serif'],
          },
          extend: {
            colors: {
              'navy': '#1A2E4C', // Dark blue, primary for dark theme elements
              'navy-light': '#2D4A70', // Lighter navy for hover or secondary elements
              'navy-dark': '#0F1A2A', // Very dark blue, often for backgrounds in dark mode
              'gold-accent': '#DAA520', // Golden accent for highlights, CTAs
              'light-gray': '#F3F4F6', // Background for light mode content areas
              'medium-gray': '#D1D5DB', // Borders, dividers, disabled states
              'dark-gray': '#4B5563', // Text color in light mode, secondary elements
              'status-green': '#10B981', // Success, available
              'status-red': '#EF4444',   // Error, occupied, danger
              'status-yellow': '#F59E0B',// Warning, cleaning, pending
              'status-blue': '#3B82F6', // Informational
              'white': '#FFFFFF',
              'black': '#000000',
            },
          },
        },
      }
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
  <script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@18.3.1",
    "react-dom/client": "https://esm.sh/react-dom@18.3.1/client",
    "react-dom/": "https://esm.sh/react-dom@18.3.1/",
    "react/": "https://esm.sh/react@18.3.1/",
    "react-router-dom": "https://esm.sh/react-router-dom@^7.6.2",
    "recharts": "https://esm.sh/recharts@^3.0.0"
  }
}
</script>
<link rel="stylesheet" href="/src/styles/globals.css">
</head>
  <body class="font-sans">
    <div id="root"></div>
    <script type="module" src="/index.tsx"></script>
  </body>
</html>