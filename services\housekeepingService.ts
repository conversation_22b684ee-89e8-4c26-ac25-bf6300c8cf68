import { HousekeepingTask } from '../types';
import { db, STORES } from '../lib/database';

export class HousekeepingService {
  async addTask(task: HousekeepingTask): Promise<void> {
    await db.add(STORES.HOUSEKEEPING_TASKS, task);
  }

  async updateTask(task: HousekeepingTask): Promise<void> {
    await db.update(STORES.HOUSEKEEPING_TASKS, task);
  }

  async getTask(id: string): Promise<HousekeepingTask | undefined> {
    const result = await db.get<HousekeepingTask>(STORES.HOUSEKEEPING_TASKS, id);
    return result.success ? result.data : undefined;
  }

  async getAllTasks(): Promise<HousekeepingTask[]> {
    const result = await db.getAll<HousekeepingTask>(STORES.HOUSEKEEPING_TASKS);
    return result.success ? result.data || [] : [];
  }

  async deleteTask(id: string): Promise<void> {
    await db.delete(STORES.HOUSEKEEPING_TASKS, id);
  }

  async getTasksByRoom(roomId: string): Promise<HousekeepingTask[]> {
    const result = await db.getByIndex<HousekeepingTask>(STORES.HOUSEKEEPING_TASKS, 'roomId', roomId);
    return result.success ? result.data || [] : [];
  }

  async getTasksByStatus(status: 'Pending' | 'In Progress' | 'Completed' | 'Blocked'): Promise<HousekeepingTask[]> {
    const result = await db.getByIndex<HousekeepingTask>(STORES.HOUSEKEEPING_TASKS, 'status', status);
    return result.success ? result.data || [] : [];
  }

  async getTasksByPriority(priority: 'Low' | 'Medium' | 'High'): Promise<HousekeepingTask[]> {
    const result = await db.getByIndex<HousekeepingTask>(STORES.HOUSEKEEPING_TASKS, 'priority', priority);
    return result.success ? result.data || [] : [];
  }

  async getTasksByAssignee(staffId: string): Promise<HousekeepingTask[]> {
    const result = await db.getByIndex<HousekeepingTask>(STORES.HOUSEKEEPING_TASKS, 'assignedTo', staffId);
    return result.success ? result.data || [] : [];
  }

  async assignTask(taskId: string, staffId: string): Promise<void> {
    const task = await this.getTask(taskId);
    if (task) {
      task.assignedTo = staffId;
      await this.updateTask(task);
    }
  }

  async startTask(taskId: string): Promise<void> {
    const task = await this.getTask(taskId);
    if (task && task.status === 'Pending') {
      task.status = 'In Progress';
      await this.updateTask(task);
    }
  }

  async completeTask(taskId: string): Promise<void> {
    const task = await this.getTask(taskId);
    if (task && task.status === 'In Progress') {
      task.status = 'Completed';
      task.completedAt = new Date();
      await this.updateTask(task);
    }
  }

  async blockTask(taskId: string): Promise<void> {
    const task = await this.getTask(taskId);
    if (task) {
      task.status = 'Blocked';
      await this.updateTask(task);
    }
  }

  async getPendingTasks(): Promise<HousekeepingTask[]> {
    return await this.getTasksByStatus('Pending');
  }

  async getInProgressTasks(): Promise<HousekeepingTask[]> {
    return await this.getTasksByStatus('In Progress');
  }

  async getCompletedTasks(): Promise<HousekeepingTask[]> {
    return await this.getTasksByStatus('Completed');
  }

  async getHighPriorityTasks(): Promise<HousekeepingTask[]> {
    return await this.getTasksByPriority('High');
  }

  async getTaskStats(): Promise<{
    total: number;
    pending: number;
    inProgress: number;
    completed: number;
    blocked: number;
    highPriority: number;
  }> {
    const allTasks = await this.getAllTasks();
    
    return {
      total: allTasks.length,
      pending: allTasks.filter(t => t.status === 'Pending').length,
      inProgress: allTasks.filter(t => t.status === 'In Progress').length,
      completed: allTasks.filter(t => t.status === 'Completed').length,
      blocked: allTasks.filter(t => t.status === 'Blocked').length,
      highPriority: allTasks.filter(t => t.priority === 'High').length
    };
  }

  async getTodaysTasks(): Promise<HousekeepingTask[]> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    const allTasks = await this.getAllTasks();
    return allTasks.filter(task => {
      const reportedDate = new Date(task.reportedAt);
      reportedDate.setHours(0, 0, 0, 0);
      return reportedDate.getTime() >= today.getTime() && reportedDate.getTime() < tomorrow.getTime();
    });
  }
}

export const housekeepingService = new HousekeepingService();
