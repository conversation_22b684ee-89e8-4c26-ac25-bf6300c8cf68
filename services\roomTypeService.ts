import { RoomType } from '../types';
import { db, STORES } from '../lib/database';

export class RoomTypeService {
  async addRoomType(roomType: RoomType): Promise<void> {
    await db.add(STORES.ROOM_TYPES, roomType);
  }

  async updateRoomType(roomType: RoomType): Promise<void> {
    await db.update(STORES.ROOM_TYPES, roomType);
  }

  async getRoomType(id: string): Promise<RoomType | undefined> {
    return await db.get<RoomType>(STORES.ROOM_TYPES, id);
  }

  async getAllRoomTypes(): Promise<RoomType[]> {
    return await db.getAll<RoomType>(STORES.ROOM_TYPES);
  }

  async deleteRoomType(id: string): Promise<void> {
    await db.delete(STORES.ROOM_TYPES, id);
  }

  async getRoomTypeByName(name: string): Promise<RoomType | undefined> {
    const roomTypes = await db.getByIndex<RoomType>(STORES.ROOM_TYPES, 'name', name);
    return roomTypes[0];
  }

  async searchRoomTypes(searchTerm: string): Promise<RoomType[]> {
    const allRoomTypes = await this.getAllRoomTypes();
    const term = searchTerm.toLowerCase();
    
    return allRoomTypes.filter(roomType => 
      roomType.name.toLowerCase().includes(term) ||
      roomType.amenities.some(amenity => amenity.toLowerCase().includes(term))
    );
  }

  async getRoomTypeStats(): Promise<{
    total: number;
    averagePrice: number;
    mostExpensive: RoomType | null;
    cheapest: RoomType | null;
  }> {
    const allRoomTypes = await this.getAllRoomTypes();
    
    if (allRoomTypes.length === 0) {
      return {
        total: 0,
        averagePrice: 0,
        mostExpensive: null,
        cheapest: null
      };
    }

    const prices = allRoomTypes.map(rt => rt.basePrice);
    const averagePrice = prices.reduce((sum, price) => sum + price, 0) / prices.length;
    const maxPrice = Math.max(...prices);
    const minPrice = Math.min(...prices);

    return {
      total: allRoomTypes.length,
      averagePrice: Math.round(averagePrice * 100) / 100,
      mostExpensive: allRoomTypes.find(rt => rt.basePrice === maxPrice) || null,
      cheapest: allRoomTypes.find(rt => rt.basePrice === minPrice) || null
    };
  }

  async initializeDefaultRoomTypes(): Promise<void> {
    const existingTypes = await this.getAllRoomTypes();
    
    // Only initialize if no room types exist
    if (existingTypes.length === 0) {
      const defaultRoomTypes: RoomType[] = [
        {
          id: 'rt-single',
          name: 'Single Room',
          basePrice: 100,
          amenities: ['Wi-Fi', 'Air Conditioning', 'TV', 'Private Bathroom'],
          capacity: 1
        },
        {
          id: 'rt-double',
          name: 'Double Room',
          basePrice: 150,
          amenities: ['Wi-Fi', 'Air Conditioning', 'TV', 'Private Bathroom', 'Mini Bar'],
          capacity: 2
        },
        {
          id: 'rt-suite',
          name: 'Suite',
          basePrice: 300,
          amenities: ['Wi-Fi', 'Air Conditioning', 'TV', 'Private Bathroom', 'Mini Bar', 'Living Area', 'Balcony'],
          capacity: 4
        }
      ];

      for (const roomType of defaultRoomTypes) {
        await this.addRoomType(roomType);
      }
    }
  }
}

export const roomTypeService = new RoomTypeService();
