
import { NavItem, UserRole } from './types';
import { HomeIcon, CalendarIcon, KeyIcon, UsersIcon, ChartBarIcon, CogIcon, ClipboardListIcon } from './components/shared/Icon';

export const APP_NAME = "Premium Stay";

export const NAVIGATION_ITEMS: NavItem[] = [
  { path: '/', name: 'Dashboard', icon: HomeIcon, allowedRoles: [UserRole.Admin, UserRole.Manager, UserRole.FrontDesk, UserRole.Housekeeping] },
  { path: '/bookings', name: 'Bookings', icon: CalendarIcon, allowedRoles: [UserRole.Admin, UserRole.Manager, UserRole.FrontDesk] },
  { path: '/rooms', name: 'Rooms', icon: KeyIcon, allowedRoles: [UserRole.Admin, UserRole.Manager, UserRole.FrontDesk, UserRole.Housekeeping] },
  { path: '/housekeeping', name: 'Housekeeping', icon: ClipboardListIcon, allowedRoles: [UserRole.Admin, UserRole.Manager, UserRole.Housekeeping] },
  { path: '/guests', name: 'Guests', icon: UsersIcon, allowedRoles: [UserRole.Admin, UserRole.Manager, UserRole.FrontDesk] },
  { path: '/reports', name: 'Reports', icon: ChartBarIcon, allowedRoles: [UserRole.Admin, UserRole.Manager] },
  { path: '/settings', name: 'Settings', icon: CogIcon, allowedRoles: [UserRole.Admin] },
];

export const USER_ROLES_OPTIONS = [
  { value: UserRole.Admin, label: 'Admin' },
  { value: UserRole.Manager, label: 'Manager' },
  { value: UserRole.FrontDesk, label: 'Front Desk' },
  { value: UserRole.Housekeeping, label: 'Housekeeping' },
];

export const ROOM_STATUS_COLORS: { [key: string]: string } = {
  Available: 'bg-status-green text-white',
  Occupied: 'bg-status-red text-white',
  Cleaning: 'bg-status-yellow text-gray-800',
  'Out of Service': 'bg-dark-gray text-white',
  'Pending Arrival': 'bg-blue-500 text-white',
  'Pending Departure': 'bg-purple-500 text-white',
};

export const BOOKING_STATUS_COLORS: { [key: string]: string } = {
  Confirmed: 'bg-status-green text-white',
  'Pending Payment': 'bg-status-yellow text-gray-800',
  Cancelled: 'bg-status-red text-white',
  'Checked In': 'bg-blue-500 text-white',
  'Checked Out': 'bg-medium-gray text-gray-800',
};
