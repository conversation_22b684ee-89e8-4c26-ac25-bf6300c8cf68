
import React from 'react';

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string | undefined;
  icon?: React.ReactNode;
}

const Input: React.FC<InputProps> = ({ label, id, error, icon, className = '', ...props }) => {
  const baseInputClasses = "block w-full px-4 py-2.5 border rounded-lg shadow-sm focus:outline-none focus:ring-2 sm:text-sm transition-colors duration-150";
  const normalClasses = "border-medium-gray dark:border-navy-light bg-white dark:bg-navy focus:ring-gold-accent dark:focus:ring-gold-accent text-dark-gray dark:text-light-gray placeholder-gray-400 dark:placeholder-gray-500";
  const errorClasses = "border-status-red dark:border-red-500 focus:ring-status-red dark:focus:ring-red-500";
  
  return (
    <div className="w-full">
      {label && <label htmlFor={id} className="block text-sm font-medium text-dark-gray dark:text-light-gray mb-1">{label}</label>}
      <div className="relative">
        {icon && <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-gray-400 dark:text-gray-500">{icon}</div>}
        <input
          id={id}
          className={`${baseInputClasses} ${error ? errorClasses : normalClasses} ${icon ? 'pl-10' : ''} ${className}`}
          {...props}
        />
      </div>
      {error && <p className="mt-1 text-xs text-status-red dark:text-red-400">{error}</p>}
    </div>
  );
};

export default Input;
