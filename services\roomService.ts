import { Room, RoomStatus } from '../types';
import { db, STORES, DatabaseResult, DatabaseError } from '../lib/database';

/**
 * Enhanced Room Service with comprehensive error handling and type safety
 */
export class RoomService {
  /**
   * Add a new room with enhanced error handling
   */
  async addRoom(room: Room): Promise<DatabaseResult<Room>> {
    try {
      const roomWithTimestamps = {
        ...room,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const result = await db.add(STORES.ROOMS, roomWithTimestamps);
      if (result.success) {
        return { success: true, data: roomWithTimestamps };
      }
      return result;
    } catch (error) {
      return {
        success: false,
        error: error instanceof DatabaseError ? error.message : 'Failed to add room'
      };
    }
  }

  /**
   * Update an existing room with validation
   */
  async updateRoom(room: Room): Promise<DatabaseResult<Room>> {
    try {
      const updatedRoom = {
        ...room,
        updatedAt: new Date()
      };

      const result = await db.update(STORES.ROOMS, updatedRoom);
      if (result.success) {
        return { success: true, data: updatedRoom };
      }
      return result;
    } catch (error) {
      return {
        success: false,
        error: error instanceof DatabaseError ? error.message : 'Failed to update room'
      };
    }
  }

  /**
   * Get a single room by ID
   */
  async getRoom(id: string): Promise<DatabaseResult<Room>> {
    try {
      return await db.get<Room>(STORES.ROOMS, id);
    } catch (error) {
      return {
        success: false,
        error: error instanceof DatabaseError ? error.message : 'Failed to get room'
      };
    }
  }

  /**
   * Get all rooms with error handling
   */
  async getAllRooms(): Promise<DatabaseResult<Room[]>> {
    try {
      return await db.getAll<Room>(STORES.ROOMS);
    } catch (error) {
      return {
        success: false,
        error: error instanceof DatabaseError ? error.message : 'Failed to get rooms'
      };
    }
  }

  /**
   * Delete a room with validation
   */
  async deleteRoom(id: string): Promise<DatabaseResult<void>> {
    try {
      // Check if room exists first
      const roomResult = await this.getRoom(id);
      if (!roomResult.success || !roomResult.data) {
        return { success: false, error: 'Room not found' };
      }

      // Check if room is currently occupied
      if (roomResult.data.status === RoomStatus.Occupied) {
        return { success: false, error: 'Cannot delete occupied room' };
      }

      return await db.delete(STORES.ROOMS, id);
    } catch (error) {
      return {
        success: false,
        error: error instanceof DatabaseError ? error.message : 'Failed to delete room'
      };
    }
  }

  /**
   * Get rooms by status with error handling
   */
  async getRoomsByStatus(status: RoomStatus): Promise<DatabaseResult<Room[]>> {
    try {
      return await db.getByIndex<Room>(STORES.ROOMS, 'status', status);
    } catch (error) {
      return {
        success: false,
        error: error instanceof DatabaseError ? error.message : 'Failed to get rooms by status'
      };
    }
  }

  /**
   * Get rooms by floor with error handling
   */
  async getRoomsByFloor(floor: number): Promise<DatabaseResult<Room[]>> {
    try {
      return await db.getByIndex<Room>(STORES.ROOMS, 'floor', floor);
    } catch (error) {
      return {
        success: false,
        error: error instanceof DatabaseError ? error.message : 'Failed to get rooms by floor'
      };
    }
  }

  /**
   * Get room by number with error handling
   */
  async getRoomByNumber(roomNumber: string): Promise<DatabaseResult<Room>> {
    try {
      const result = await db.getByIndex<Room>(STORES.ROOMS, 'roomNumber', roomNumber);
      if (result.success && result.data && result.data.length > 0) {
        return { success: true, data: result.data[0] };
      }
      return { success: false, error: 'Room not found' };
    } catch (error) {
      return {
        success: false,
        error: error instanceof DatabaseError ? error.message : 'Failed to get room by number'
      };
    }
  }

  /**
   * Update room status with validation
   */
  async updateRoomStatus(
    roomId: string,
    status: RoomStatus,
    currentBookingId?: string | null
  ): Promise<DatabaseResult<Room>> {
    try {
      const roomResult = await this.getRoom(roomId);
      if (!roomResult.success || !roomResult.data) {
        return { success: false, error: 'Room not found' };
      }

      const updatedRoom = {
        ...roomResult.data,
        status,
        currentBookingId: currentBookingId !== undefined ? currentBookingId : (roomResult.data.currentBookingId || null),
        updatedAt: new Date()
      };

      return await this.updateRoom(updatedRoom);
    } catch (error) {
      return {
        success: false,
        error: error instanceof DatabaseError ? error.message : 'Failed to update room status'
      };
    }
  }

  /**
   * Get available rooms
   */
  async getAvailableRooms(): Promise<DatabaseResult<Room[]>> {
    return await this.getRoomsByStatus(RoomStatus.Available);
  }

  /**
   * Get occupied rooms
   */
  async getOccupiedRooms(): Promise<DatabaseResult<Room[]>> {
    return await this.getRoomsByStatus(RoomStatus.Occupied);
  }

  /**
   * Get rooms needing cleaning
   */
  async getRoomsNeedingCleaning(): Promise<DatabaseResult<Room[]>> {
    return await this.getRoomsByStatus(RoomStatus.Cleaning);
  }

  /**
   * Get out of service rooms
   */
  async getOutOfServiceRooms(): Promise<DatabaseResult<Room[]>> {
    return await this.getRoomsByStatus(RoomStatus.OutOfService);
  }

  /**
   * Get comprehensive room statistics
   */
  async getRoomStats(): Promise<DatabaseResult<{
    total: number;
    available: number;
    occupied: number;
    cleaning: number;
    outOfService: number;
    occupancyRate: number;
  }>> {
    try {
      const allRoomsResult = await this.getAllRooms();
      if (!allRoomsResult.success || !allRoomsResult.data) {
        return { success: false, error: 'Failed to get rooms for statistics' };
      }

      const allRooms = allRoomsResult.data;
      const available = allRooms.filter(r => r.status === RoomStatus.Available).length;
      const occupied = allRooms.filter(r => r.status === RoomStatus.Occupied).length;
      const cleaning = allRooms.filter(r => r.status === RoomStatus.Cleaning).length;
      const outOfService = allRooms.filter(r => r.status === RoomStatus.OutOfService).length;

      return {
        success: true,
        data: {
          total: allRooms.length,
          available,
          occupied,
          cleaning,
          outOfService,
          occupancyRate: allRooms.length > 0 ? (occupied / allRooms.length) * 100 : 0
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof DatabaseError ? error.message : 'Failed to calculate room statistics'
      };
    }
  }
}

export const roomService = new RoomService();
