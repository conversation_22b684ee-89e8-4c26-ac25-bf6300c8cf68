import React from 'react';
import { Guest } from '../../types';
import Button from '../shared/Button';
import Badge from '../shared/Badge';
import { EditIcon, EyeIcon, TrashIcon } from '../shared/Icon';

interface CustomerListProps {
  customers: Guest[];
  onViewDetails: (customerId: string) => void;
  onEdit: (customerId: string) => void;
  onDelete: (customerId: string) => void;
  loading?: boolean;
}

const CustomerList: React.FC<CustomerListProps> = ({
  customers,
  onViewDetails,
  onEdit,
  onDelete,
  loading = false
}) => {
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString();
  };

  const getCustomerStats = (customer: Guest) => {
    const bookingsCount = customer.pastBookings?.length || 0;
    const preferencesCount = customer.preferences?.length || 0;
    
    return { bookingsCount, preferencesCount };
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (customers.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500 dark:text-gray-400 text-lg">
          No customers found.
        </p>
        <p className="text-gray-400 dark:text-gray-500 text-sm mt-2">
          Add your first customer to get started.
        </p>
      </div>
    );
  }

  return (
    <div className="overflow-x-auto shadow-md rounded-lg">
      <table className="min-w-full divide-y divide-gray-200 dark:divide-navy-light">
        <thead className="bg-gray-50 dark:bg-navy">
          <tr>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Customer
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Contact
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Stats
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Joined
            </th>
            <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody className="bg-white dark:bg-navy divide-y divide-gray-200 dark:divide-navy-light">
          {customers.map((customer) => {
            const { bookingsCount, preferencesCount } = getCustomerStats(customer);
            
            return (
              <tr key={customer.id} className="hover:bg-gray-50 dark:hover:bg-navy-light">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-10 w-10">
                      <div className="h-10 w-10 rounded-full bg-gold-accent flex items-center justify-center">
                        <span className="text-sm font-medium text-white">
                          {customer.firstName.charAt(0)}{customer.lastName.charAt(0)}
                        </span>
                      </div>
                    </div>
                    <div className="ml-4">
                      <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {customer.firstName} {customer.lastName}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        ID: {customer.id}
                      </div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900 dark:text-gray-100">
                    {customer.email}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    {customer.phone}
                  </div>
                  {customer.address && (
                    <div className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                      {customer.address}
                    </div>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex flex-col space-y-1">
                    <Badge variant={bookingsCount > 0 ? 'success' : 'secondary'}>
                      {bookingsCount} Booking{bookingsCount !== 1 ? 's' : ''}
                    </Badge>
                    {preferencesCount > 0 && (
                      <Badge variant="info">
                        {preferencesCount} Preference{preferencesCount !== 1 ? 's' : ''}
                      </Badge>
                    )}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  {formatDate(customer.createdAt)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div className="flex justify-end space-x-2">
                    <Button
                      variant="secondary"
                      size="sm"
                      onClick={() => onViewDetails(customer.id)}
                      leftIcon={<EyeIcon className="w-4 h-4" />}
                    >
                      View
                    </Button>
                    <Button
                      variant="primary"
                      size="sm"
                      onClick={() => onEdit(customer.id)}
                      leftIcon={<EditIcon className="w-4 h-4" />}
                    >
                      Edit
                    </Button>
                    <Button
                      variant="danger"
                      size="sm"
                      onClick={() => onDelete(customer.id)}
                      leftIcon={<TrashIcon className="w-4 h-4" />}
                    >
                      Delete
                    </Button>
                  </div>
                </td>
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
};

export default CustomerList;
