import { RoomType } from '../types';
import { db, STORES, DatabaseResult } from '../lib/database';

export class RoomTypeService {
  async addRoomType(roomType: RoomType): Promise<void> {
    await db.add(STORES.ROOM_TYPES, roomType);
  }

  async updateRoomType(roomType: RoomType): Promise<void> {
    await db.update(STORES.ROOM_TYPES, roomType);
  }

  async getRoomType(id: string): Promise<RoomType | undefined> {
    const result = await db.get<RoomType>(STORES.ROOM_TYPES, id);
    return result.success ? result.data : undefined;
  }

  async getAllRoomTypes(): Promise<DatabaseResult<RoomType[]>> {
    return await db.getAll<RoomType>(STORES.ROOM_TYPES);
  }

  async deleteRoomType(id: string): Promise<void> {
    await db.delete(STORES.ROOM_TYPES, id);
  }

  async getRoomTypeByName(name: string): Promise<RoomType | undefined> {
    const result = await db.getByIndex<RoomType>(STORES.ROOM_TYPES, 'name', name);
    if (!result.success || !result.data || result.data.length === 0) return undefined;
    return result.data[0];
  }

  async searchRoomTypes(searchTerm: string): Promise<RoomType[]> {
    const result = await this.getAllRoomTypes();
    if (!result.success || !result.data) return [];

    const term = searchTerm.toLowerCase();

    return result.data.filter((roomType: RoomType) =>
      roomType.name.toLowerCase().includes(term) ||
      roomType.amenities.some((amenity: string) => amenity.toLowerCase().includes(term))
    );
  }

  async getRoomTypeStats(): Promise<{
    total: number;
    averagePrice: number;
    mostExpensive: RoomType | null;
    cheapest: RoomType | null;
  }> {
    const allRoomTypes = await this.getAllRoomTypes();
    
    if (allRoomTypes.length === 0) {
      return {
        total: 0,
        averagePrice: 0,
        mostExpensive: null,
        cheapest: null
      };
    }

    const prices = allRoomTypes.map(rt => rt.basePrice);
    const averagePrice = prices.reduce((sum, price) => sum + price, 0) / prices.length;
    const maxPrice = Math.max(...prices);
    const minPrice = Math.min(...prices);

    return {
      total: allRoomTypes.length,
      averagePrice: Math.round(averagePrice * 100) / 100,
      mostExpensive: allRoomTypes.find(rt => rt.basePrice === maxPrice) || null,
      cheapest: allRoomTypes.find(rt => rt.basePrice === minPrice) || null
    };
  }

  async initializeDefaultRoomTypes(): Promise<void> {
    const result = await this.getAllRoomTypes();

    // Only initialize if no room types exist
    if (!result.success || !result.data || result.data.length === 0) {
      const defaultRoomTypes: RoomType[] = [
        {
          id: 'rt-single',
          name: 'Single Room',
          basePrice: 100,
          amenities: ['Wi-Fi', 'Air Conditioning', 'TV', 'Private Bathroom'],
          capacity: 1,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: 'rt-double',
          name: 'Double Room',
          basePrice: 150,
          amenities: ['Wi-Fi', 'Air Conditioning', 'TV', 'Private Bathroom', 'Mini Bar'],
          capacity: 2,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: 'rt-suite',
          name: 'Suite',
          basePrice: 300,
          amenities: ['Wi-Fi', 'Air Conditioning', 'TV', 'Private Bathroom', 'Mini Bar', 'Living Area', 'Balcony'],
          capacity: 4,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];

      for (const roomType of defaultRoomTypes) {
        await this.addRoomType(roomType);
      }
    }
  }
}

export const roomTypeService = new RoomTypeService();
