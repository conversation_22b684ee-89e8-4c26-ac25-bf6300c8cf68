
import React from 'react';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'danger' | 'success' | 'ghost' | 'link';
  size?: 'sm' | 'md' | 'lg';
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  isLoading?: boolean;
}

const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  leftIcon,
  rightIcon,
  isLoading = false,
  className = '',
  ...props
}) => {
  const baseStyles = "font-medium rounded-lg focus:outline-none focus:ring-2 focus:ring-opacity-50 transition-all duration-150 ease-in-out inline-flex items-center justify-center";

  const variantStyles = {
    primary: "bg-navy hover:bg-navy-light text-white focus:ring-navy-light dark:bg-gold-accent dark:hover:bg-amber-500 dark:focus:ring-amber-500 dark:text-navy-dark",
    secondary: "bg-light-gray hover:bg-medium-gray text-navy-dark focus:ring-medium-gray dark:bg-navy-light dark:hover:bg-navy dark:text-light-gray dark:focus:ring-navy",
    danger: "bg-status-red hover:bg-red-600 text-white focus:ring-red-600",
    success: "bg-green-600 hover:bg-green-700 text-white focus:ring-green-600",
    ghost: "bg-transparent hover:bg-gray-100 dark:hover:bg-navy-light text-navy-dark dark:text-light-gray focus:ring-gray-300 dark:focus:ring-navy",
    link: "bg-transparent text-navy hover:underline dark:text-gold-accent dark:hover:underline focus:ring-transparent",
  };

  const sizeStyles = {
    sm: "px-3 py-1.5 text-sm",
    md: "px-4 py-2 text-base",
    lg: "px-6 py-3 text-lg",
  };

  const loadingStyles = isLoading ? "opacity-75 cursor-not-allowed" : "";

  return (
    <button
      className={`${baseStyles} ${variantStyles[variant]} ${sizeStyles[size]} ${loadingStyles} ${className}`}
      disabled={isLoading || props.disabled}
      {...props}
    >
      {isLoading && (
        <svg className="animate-spin -ml-1 mr-3 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      )}
      {leftIcon && !isLoading && <span className="mr-2">{leftIcon}</span>}
      {children}
      {rightIcon && !isLoading && <span className="ml-2">{rightIcon}</span>}
    </button>
  );
};

export default Button;
