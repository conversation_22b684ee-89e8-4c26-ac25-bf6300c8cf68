
import React from 'react';

import { ROOM_STATUS_COLORS } from '../../constants';
import Card from '../shared/Card';

const RoomStatusLegend: React.FC = () => {
  return (
    <Card title="Status Legend" className="mb-6">
      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-2">
        {Object.entries(ROOM_STATUS_COLORS).map(([status, colorClass]) => (
          <div key={status} className="flex items-center space-x-2">
            <span className={`w-4 h-4 rounded-full inline-block ${colorClass.split(' ')[0]}`}></span>
            <span className="text-sm text-dark-gray dark:text-light-gray">{status}</span>
          </div>
        ))}
      </div>
    </Card>
  );
};

export default RoomStatusLegend;
