
import React from 'react';
import { Room, RoomStatus } from '../../types';
import Card from '../shared/Card';
import Badge from '../shared/Badge';
import Button from '../shared/Button';
import { EyeIcon } from '../shared/Icon';
import { ROOM_STATUS_COLORS } from '../../constants';

interface RoomCardProps {
  room: Room;
  onViewDetails: (roomId: string) => void;
  onEditStatus: (roomId: string, newStatus: RoomStatus) => void;
}

const RoomCard: React.FC<RoomCardProps> = ({ room, onViewDetails, onEditStatus }) => {
  // Simplified status change for demo
  const handleQuickStatusChange = () => {
    if (room.status === RoomStatus.Available) onEditStatus(room.id, RoomStatus.Cleaning);
    else if (room.status === RoomStatus.Cleaning) onEditStatus(room.id, RoomStatus.Available);
  };
  
  const statusColor = ROOM_STATUS_COLORS[room.status] || 'bg-gray-200 text-gray-800';

  return (
    <Card className={`border-l-4 ${
        room.status === RoomStatus.Available ? 'border-status-green' : 
        room.status === RoomStatus.Occupied ? 'border-status-red' :
        room.status === RoomStatus.Cleaning ? 'border-status-yellow' :
        room.status === RoomStatus.OutOfService ? 'border-dark-gray' :
        'border-blue-500' // For pending states
      } transform hover:shadow-lg transition-shadow duration-200`}>
      <div className="flex justify-between items-start mb-2">
        <h3 className="text-xl font-poppins font-semibold text-navy-dark dark:text-gold-accent">Room {room.roomNumber}</h3>
        <Badge colorClass={statusColor}>{room.status}</Badge>
      </div>
      <p className="text-sm text-gray-600 dark:text-gray-300 mb-1">Type: {room.type.name}</p>
      <p className="text-sm text-gray-600 dark:text-gray-300 mb-1">Floor: {room.floor}</p>
      <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">Price: ${room.type.basePrice}/night</p>
      
      {room.notes && <p className="text-xs text-gray-500 dark:text-gray-400 mb-3 italic">Note: {room.notes}</p>}

      <div className="flex justify-end space-x-2 mt-4">
        { (room.status === RoomStatus.Available || room.status === RoomStatus.Cleaning) &&
          <Button variant="secondary" size="sm" onClick={handleQuickStatusChange}>
            {room.status === RoomStatus.Available ? 'Mark Cleaning' : 'Mark Available'}
          </Button>
        }
        <Button variant="ghost" size="sm" onClick={() => onViewDetails(room.id)} title="View Details">
          <EyeIcon className="w-5 h-5 text-blue-500" />
        </Button>
        {/* <Button variant="ghost" size="sm" onClick={() => {}} title="Edit Room">
          <EditIcon className="w-5 h-5 text-yellow-500" />
        </Button> */}
      </div>
    </Card>
  );
};

export default RoomCard;
