import React from 'react';
import { Room, RoomStatus } from '../../types';
import Button from '../shared/Button';
import Badge from '../shared/Badge';
import { EditIcon, EyeIcon, TrashIcon } from '../shared/Icon';

interface RoomListProps {
  rooms: Room[];
  onViewDetails: (roomId: string) => void;
  onEdit: (roomId: string) => void;
  onDelete: (roomId: string) => void;
  onUpdateStatus: (roomId: string, status: RoomStatus) => void;
  loading?: boolean;
}

const RoomList: React.FC<RoomListProps> = ({
  rooms,
  onViewDetails,
  onEdit,
  onDelete,
  onUpdateStatus,
  loading = false
}) => {
  const getStatusColor = (status: RoomStatus) => {
    switch (status) {
      case RoomStatus.Available:
        return 'success';
      case RoomStatus.Occupied:
        return 'danger';
      case RoomStatus.Cleaning:
        return 'warning';
      case RoomStatus.OutOfService:
        return 'secondary';
      case RoomStatus.PendingArrival:
        return 'info';
      case RoomStatus.PendingDeparture:
        return 'info';
      default:
        return 'secondary';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const getStatusOptions = (currentStatus: RoomStatus) => {
    return Object.values(RoomStatus).filter(status => status !== currentStatus);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (rooms.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500 dark:text-gray-400 text-lg">
          No rooms found.
        </p>
        <p className="text-gray-400 dark:text-gray-500 text-sm mt-2">
          Add your first room to get started.
        </p>
      </div>
    );
  }

  return (
    <div className="overflow-x-auto shadow-md rounded-lg">
      <table className="min-w-full divide-y divide-gray-200 dark:divide-navy-light">
        <thead className="bg-gray-50 dark:bg-navy">
          <tr>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Room
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Type & Price
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Status
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Floor
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Amenities
            </th>
            <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody className="bg-white dark:bg-navy divide-y divide-gray-200 dark:divide-navy-light">
          {rooms.map((room) => (
            <tr key={room.id} className="hover:bg-gray-50 dark:hover:bg-navy-light">
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                  <div className="flex-shrink-0 h-10 w-10">
                    <div className="h-10 w-10 rounded-lg bg-navy dark:bg-gold-accent flex items-center justify-center">
                      <span className="text-sm font-bold text-white">
                        {room.roomNumber}
                      </span>
                    </div>
                  </div>
                  <div className="ml-4">
                    <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      Room {room.roomNumber}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      ID: {room.id}
                    </div>
                  </div>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm text-gray-900 dark:text-gray-100">
                  {room.type.name}
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  {formatCurrency(room.type.basePrice)}/night
                </div>
                <div className="text-xs text-gray-400 dark:text-gray-500">
                  Capacity: {room.type.capacity} guest{room.type.capacity !== 1 ? 's' : ''}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex flex-col space-y-2">
                  <Badge variant={getStatusColor(room.status) as any}>
                    {room.status}
                  </Badge>
                  {room.status !== RoomStatus.Available && (
                    <select
                      className="text-xs border border-gray-300 dark:border-navy-light rounded px-2 py-1 bg-white dark:bg-navy-light text-gray-900 dark:text-gray-100"
                      onChange={(e) => onUpdateStatus(room.id, e.target.value as RoomStatus)}
                      value=""
                    >
                      <option value="">Change Status</option>
                      {getStatusOptions(room.status).map((status) => (
                        <option key={status} value={status}>
                          {status}
                        </option>
                      ))}
                    </select>
                  )}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                Floor {room.floor}
              </td>
              <td className="px-6 py-4">
                <div className="flex flex-wrap gap-1">
                  {room.type.amenities.slice(0, 3).map((amenity, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 dark:bg-navy-light text-gray-800 dark:text-gray-200"
                    >
                      {amenity}
                    </span>
                  ))}
                  {room.type.amenities.length > 3 && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 dark:bg-navy-light text-gray-800 dark:text-gray-200">
                      +{room.type.amenities.length - 3} more
                    </span>
                  )}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div className="flex justify-end space-x-2">
                  <Button
                    variant="secondary"
                    size="sm"
                    onClick={() => onViewDetails(room.id)}
                    leftIcon={<EyeIcon className="w-4 h-4" />}
                  >
                    View
                  </Button>
                  <Button
                    variant="primary"
                    size="sm"
                    onClick={() => onEdit(room.id)}
                    leftIcon={<EditIcon className="w-4 h-4" />}
                  >
                    Edit
                  </Button>
                  <Button
                    variant="danger"
                    size="sm"
                    onClick={() => onDelete(room.id)}
                    leftIcon={<TrashIcon className="w-4 h-4" />}
                    disabled={room.status === RoomStatus.Occupied}
                  >
                    Delete
                  </Button>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default RoomList;
