
import React from 'react';

interface CardProps {
  children: React.ReactNode;
  className?: string;
  title?: string;
  actions?: React.ReactNode;
}

const Card: React.FC<CardProps> = ({ children, className = '', title, actions }) => {
  return (
    <div className={`bg-white dark:bg-navy shadow-lg rounded-xl overflow-hidden transition-all duration-300 hover:shadow-xl ${className}`}>
      {(title || actions) && (
        <div className="px-6 py-4 border-b border-light-gray dark:border-navy-light flex justify-between items-center">
          {title && <h3 className="text-xl font-poppins font-semibold text-navy-dark dark:text-gold-accent">{title}</h3>}
          {actions && <div className="flex space-x-2">{actions}</div>}
        </div>
      )}
      <div className="p-6">
        {children}
      </div>
    </div>
  );
};

export default Card;
