import React from 'react';
import Button from './Button';
import { SaveIcon } from './Icon';

interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'number' | 'tel' | 'date' | 'textarea' | 'select';
  required?: boolean;
  placeholder?: string;
  options?: { value: string; label: string }[];
  validation?: (value: any) => string | undefined;
  disabled?: boolean;
  rows?: number; // for textarea
  min?: number; // for number inputs
  max?: number; // for number inputs
}

interface FormProps {
  fields: FormField[];
  values: Record<string, any>;
  errors: Record<string, string>;
  onChange: (name: string, value: any) => void;
  onSubmit: (e: React.FormEvent) => void;
  onCancel?: () => void;
  loading?: boolean;
  submitText?: string;
  cancelText?: string;
  showCancel?: boolean;
  className?: string;
}

const Form: React.FC<FormProps> = ({
  fields,
  values,
  errors,
  onChange,
  onSubmit,
  onCancel,
  loading = false,
  submitText = 'Save',
  cancelText = 'Cancel',
  showCancel = true,
  className = ''
}) => {
  const renderField = (field: FormField) => {
    const commonProps = {
      id: field.name,
      name: field.name,
      value: values[field.name] || '',
      onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
        const value = field.type === 'number' ? parseFloat(e.target.value) || 0 : e.target.value;
        onChange(field.name, value);
      },
      disabled: field.disabled || loading,
      required: field.required,
      placeholder: field.placeholder,
      className: `w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gold-accent focus:border-transparent bg-white dark:bg-navy-light text-gray-900 dark:text-gray-100 ${
        errors[field.name] 
          ? 'border-red-500 dark:border-red-400' 
          : 'border-gray-300 dark:border-navy-light'
      }`
    };

    switch (field.type) {
      case 'textarea':
        return (
          <textarea
            {...commonProps}
            rows={field.rows || 3}
          />
        );
      
      case 'select':
        return (
          <select {...commonProps}>
            <option value="">Select {field.label}</option>
            {field.options?.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );
      
      case 'number':
        return (
          <input
            {...commonProps}
            type="number"
            min={field.min}
            max={field.max}
          />
        );
      
      default:
        return (
          <input
            {...commonProps}
            type={field.type}
          />
        );
    }
  };

  return (
    <form onSubmit={onSubmit} className={`space-y-4 ${className}`}>
      {fields.map((field) => (
        <div key={field.name}>
          <label 
            htmlFor={field.name}
            className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
          >
            {field.label}
            {field.required && <span className="text-red-500 ml-1">*</span>}
          </label>
          
          {renderField(field)}
          
          {errors[field.name] && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {errors[field.name]}
            </p>
          )}
        </div>
      ))}

      <div className="flex justify-end space-x-3 pt-4">
        {showCancel && onCancel && (
          <Button
            type="button"
            variant="secondary"
            onClick={onCancel}
            disabled={loading}
          >
            {cancelText}
          </Button>
        )}
        <Button
          type="submit"
          variant="primary"
          leftIcon={<SaveIcon className="w-4 h-4" />}
          disabled={loading}
        >
          {loading ? 'Saving...' : submitText}
        </Button>
      </div>
    </form>
  );
};

export default Form;
