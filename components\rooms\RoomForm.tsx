import React, { useState, useEffect } from 'react';
import { Room, RoomType, RoomStatus, RoomFormData, RoomFormErrors } from '../../types';
import { roomService } from '../../services/roomService';
import { roomTypeService } from '../../services/roomTypeService';
import { useFormValidation, ValidationRules } from '../../hooks/useFormValidation';
import { useAsyncOperation } from '../../hooks/useAsyncOperation';
import { useNotifications } from '../../hooks/useNotifications';
import Button from '../shared/Button';
import { FormField, Input, Select, Textarea } from '../shared/AccessibleForm';
import Card from '../shared/Card';
import { LoadingOverlay } from '../shared/LoadingStates';
import { SaveIcon, XIcon } from '../shared/Icon';

interface RoomFormProps {
  room?: Room | undefined;
  onSave: (room: Room) => void;
  onCancel: () => void;
  isOpen: boolean;
}

const RoomForm: React.FC<RoomFormProps> = ({
  room,
  onSave,
  onCancel,
  isOpen
}) => {
  const [roomTypes, setRoomTypes] = useState<RoomType[]>([]);

  // Enhanced form validation
  const {
    values,
    errors,
    isValid,
    isSubmitting,
    setFieldValue,
    setFieldTouched,
    validateForm,
    setSubmitting,
    resetForm
  } = useFormValidation(
    {
      roomNumber: room?.roomNumber || '',
      typeId: room?.type.id || '',
      floor: room?.floor || 1,
      notes: room?.notes || ''
    },
    {
      roomNumber: {
        required: true,
        rules: [
          ValidationRules.minLength(1),
          ValidationRules.pattern(/^[A-Za-z0-9-]+$/, 'Room number can only contain letters, numbers, and hyphens')
        ]
      },
      typeId: {
        required: true
      },
      floor: {
        required: true,
        rules: [
          ValidationRules.min(1),
          ValidationRules.max(50)
        ]
      },
      notes: {
        required: false,
        rules: [
          ValidationRules.maxLength(500)
        ]
      }
    }
  );

  // Enhanced async operations
  const { state: roomTypesState, execute: loadRoomTypes } = useAsyncOperation<RoomType[]>([], {
    onError: (error) => notifications.error('Failed to load room types', error)
  });

  const { state: saveState, execute: saveRoom } = useAsyncOperation(null);

  const notifications = useNotifications();

  useEffect(() => {
    if (isOpen) {
      const fetchRoomTypes = async () => {
        await loadRoomTypes(async () => {
          const result = await roomTypeService.getAllRoomTypes();
          if (!result.success) {
            throw new Error(result.error || 'Failed to load room types');
          }
          setRoomTypes(result.data || []);
          return result.data || [];
        });
      };

      fetchRoomTypes();
    }
  }, [isOpen, loadRoomTypes]);

  useEffect(() => {
    if (room) {
      setFieldValue('roomNumber', room.roomNumber);
      setFieldValue('typeId', room.type.id);
      setFieldValue('floor', room.floor);
      setFieldValue('notes', room.notes || '');
    } else {
      resetForm();
    }
  }, [room, isOpen, setFieldValue, resetForm]);

  /**
   * Enhanced form submission with validation and error handling
   */
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      notifications.warning('Please fix the form errors before submitting');
      return;
    }

    setSubmitting(true);

    await saveRoom(async () => {
      // Check if room number already exists (for new rooms or different room)
      const existingRoomResult = await roomService.getRoomByNumber(values.roomNumber.trim());
      if (existingRoomResult.success && existingRoomResult.data && existingRoomResult.data.id !== room?.id) {
        throw new Error('Room number already exists');
      }

      // Find the selected room type
      const selectedRoomType = roomTypes.find(rt => rt.id === values.typeId);
      if (!selectedRoomType) {
        throw new Error('Selected room type not found');
      }

      const roomData: Room = {
        id: room?.id || `room-${Date.now()}`,
        roomNumber: values.roomNumber.trim(),
        type: selectedRoomType,
        status: room?.status || RoomStatus.Available,
        floor: values.floor,
        notes: values.notes || undefined,
        currentBookingId: room?.currentBookingId || null,
        createdAt: room?.createdAt || new Date(),
        updatedAt: new Date()
      };

      let result;
      if (room) {
        result = await roomService.updateRoom(roomData);
      } else {
        result = await roomService.addRoom(roomData);
      }

      if (!result.success) {
        throw new Error(result.error || 'Failed to save room');
      }

      notifications.success(
        room ? 'Room updated successfully' : 'Room created successfully',
        `Room ${roomData.roomNumber} has been ${room ? 'updated' : 'created'}.`
      );

      onSave(roomData);
      return roomData;
    });

    setSubmitting(false);
  };



  if (!isOpen) return null;

  const roomTypeOptions = roomTypes.map(type => ({
    value: type.id,
    label: `${type.name} - $${type.basePrice}/night`,
    disabled: false
  }));

  const floorOptions = Array.from({ length: 20 }, (_, i) => ({
    value: (i + 1).toString(),
    label: `Floor ${i + 1}`,
    disabled: false
  }));

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-navy rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
        <LoadingOverlay isLoading={roomTypesState.loading || saveState.loading}>
          <Card>
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-poppins font-semibold text-navy-dark dark:text-gold-accent">
              {room ? 'Edit Room' : 'Add New Room'}
            </h2>
            <Button
              variant="secondary"
              onClick={onCancel}
              leftIcon={<XIcon className="w-4 h-4" />}
            >
              Close
            </Button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <FormField
              label="Room Number"
              error={errors.roomNumber || undefined}
              hint="Enter a unique room identifier (e.g., 101, A-205)"
              required
            >
              <Input
                value={values.roomNumber}
                onChange={(e) => setFieldValue('roomNumber', e.target.value)}
                onBlur={() => setFieldTouched('roomNumber')}
                placeholder="e.g., 101, A-205"
                error={!!errors.roomNumber}
              />
            </FormField>

            <FormField
              label="Room Type"
              error={errors.typeId || undefined}
              hint="Select the type of room and its pricing"
              required
            >
              <Select
                options={roomTypeOptions}
                value={values.typeId}
                onChange={(e) => setFieldValue('typeId', e.target.value)}
                onBlur={() => setFieldTouched('typeId')}
                placeholder="Select room type"
                error={!!errors.typeId}
              />
            </FormField>

            <FormField
              label="Floor"
              error={errors.floor || undefined}
              hint="Select which floor this room is located on"
              required
            >
              <Select
                options={floorOptions}
                value={values.floor.toString()}
                onChange={(e) => setFieldValue('floor', parseInt(e.target.value))}
                onBlur={() => setFieldTouched('floor')}
                error={!!errors.floor}
              />
            </FormField>

            <FormField
              label="Notes"
              error={errors.notes || undefined}
              hint="Optional additional information about this room"
            >
              <Textarea
                value={values.notes || ''}
                onChange={(e) => setFieldValue('notes', e.target.value)}
                onBlur={() => setFieldTouched('notes')}
                rows={3}
                placeholder="Optional notes about this room..."
                error={!!errors.notes}
                resize="vertical"
              />
            </FormField>

            <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
              <Button
                type="button"
                variant="secondary"
                onClick={onCancel}
                disabled={isSubmitting || roomTypesState.loading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="primary"
                leftIcon={<SaveIcon className="w-4 h-4" />}
                disabled={!isValid || isSubmitting || roomTypesState.loading}
                isLoading={isSubmitting}
              >
                {room ? 'Update Room' : 'Add Room'}
              </Button>
            </div>
          </form>
        </Card>
        </LoadingOverlay>
      </div>
    </div>
  );
};

export default RoomForm;
