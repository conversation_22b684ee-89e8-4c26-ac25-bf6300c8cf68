
import React, { useState, useEffect } from 'react';
import Card from '../shared/Card';
import { Booking, BookingStatus, Guest, HousekeepingTask } from '../../types';
import { bookingService } from '../../services/bookingService';
import { housekeepingService } from '../../services/housekeepingService';
import { EyeIcon, KeyIcon, ClipboardListIcon } from '../shared/Icon';
import Button from '../shared/Button';

interface ActivityItem {
  id: string;
  type: 'Booking' | 'Check-in' | 'Check-out' | 'Housekeeping';
  timestamp: Date;
  description: string;
  details?: Booking | HousekeepingTask | Guest;
  icon: React.ReactNode;
  color: string; // Tailwind color class for icon background
}

const ActivityFeed: React.FC = () => {
  const [activities, setActivities] = useState<ActivityItem[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadActivityData = async () => {
      try {
        const [allBookings, allTasks] = await Promise.all([
          bookingService.getAllBookings(),
          housekeepingService.getAllTasks()
        ]);

        // Map recent bookings to activity items
        const recentBookingsMapped = allBookings
          .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
          .slice(0, 3)
          .map((b): ActivityItem => ({
            id: b.id,
            type: b.status === BookingStatus.CheckedIn ? 'Check-in' : 'Booking',
            timestamp: b.createdAt,
            description: `${b.status === BookingStatus.CheckedIn ? 'Checked in:' : 'New booking:'} ${b.guest.firstName} ${b.guest.lastName} for Room ${b.room.roomNumber}`,
            details: b,
            icon: <KeyIcon className="w-5 h-5 text-white" />,
            color: b.status === BookingStatus.CheckedIn ? 'bg-status-green' : 'bg-blue-500',
          }));

        // Map recent tasks to activity items
        const recentTasksMapped = allTasks
          .filter(t => t.status === 'Pending' || t.status === 'In Progress')
          .sort((a, b) => b.reportedAt.getTime() - a.reportedAt.getTime())
          .slice(0, 2)
          .map((t): ActivityItem => ({
            id: t.id,
            type: 'Housekeeping',
            timestamp: t.reportedAt,
            description: `${t.taskType} task for Room ${t.roomNumber} (${t.status})`,
            details: t,
            icon: <ClipboardListIcon className="w-5 h-5 text-white" />,
            color: t.priority === 'High' ? 'bg-status-red' : 'bg-status-yellow',
          }));

        // Combine and sort activities
        const combinedActivities = [...recentBookingsMapped, ...recentTasksMapped]
          .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
          .slice(0, 5);

        setActivities(combinedActivities);
      } catch (error) {
        console.error('Error loading activity data:', error);
        setActivities([]);
      } finally {
        setLoading(false);
      }
    };

    loadActivityData();
  }, []);


  if (loading) {
    return (
      <Card title="Recent Activity" className="h-full">
        <div className="flex items-center justify-center h-32">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
        </div>
      </Card>
    );
  }

  return (
    <Card title="Recent Activity" className="h-full">
      {activities.length === 0 ? (
        <p className="text-gray-500 dark:text-gray-400">No recent activity.</p>
      ) : (
        <ul className="space-y-4">
          {activities.map((activity) => (
            <li key={activity.id} className="flex items-start space-x-3 group">
              <div className={`flex-shrink-0 w-10 h-10 rounded-full ${activity.color} flex items-center justify-center`}>
                {activity.icon}
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-dark-gray dark:text-light-gray">{activity.description}</p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {activity.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })} - {activity.timestamp.toLocaleDateString()}
                </p>
              </div>
              <Button variant="ghost" size="sm" className="opacity-0 group-hover:opacity-100 transition-opacity">
                <EyeIcon className="w-4 h-4" />
              </Button>
            </li>
          ))}
        </ul>
      )}
    </Card>
  );
};

export default ActivityFeed;