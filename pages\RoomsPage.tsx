
import React, { useState, useEffect } from 'react';
import { Room, UserRole, RoomStatus, RoomType } from '../types';
import { roomService } from '../services/roomService';
import { roomTypeService } from '../services/roomTypeService';
import RoomStatusLegend from '../components/rooms/RoomStatusLegend';
import RoomForm from '../components/rooms/RoomForm';
import RoomList from '../components/rooms/RoomList';
import RoomDetails from '../components/rooms/RoomDetails';
import Button from '../components/shared/Button';
import Input from '../components/shared/Input';
import Select from '../components/shared/Select';
import Card from '../components/shared/Card';
import { PlusIcon, SearchIcon } from '../components/shared/Icon';

interface RoomsPageProps {
  userRole: UserRole;
}

const RoomsPage: React.FC<RoomsPageProps> = ({ userRole: _userRole }) => {
  const [rooms, setRooms] = useState<Room[]>([]);
  const [filteredRooms, setFilteredRooms] = useState<Room[]>([]);
  const [roomTypes, setRoomTypes] = useState<RoomType[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<RoomStatus | ''>('');
  const [typeFilter, setTypeFilter] = useState<string | ''>(''); // RoomType ID

  // Modal states
  const [showForm, setShowForm] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [selectedRoom, setSelectedRoom] = useState<Room | undefined>();

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    filterRooms();
  }, [rooms, searchTerm, statusFilter, typeFilter]);

  const loadData = async () => {
    try {
      setLoading(true);
      const [allRooms, allRoomTypes] = await Promise.all([
        roomService.getAllRooms(),
        roomTypeService.getAllRoomTypes()
      ]);
      setRooms(allRooms);
      setRoomTypes(allRoomTypes);

      // Initialize default room types if none exist
      if (allRoomTypes.length === 0) {
        await roomTypeService.initializeDefaultRoomTypes();
        const newRoomTypes = await roomTypeService.getAllRoomTypes();
        setRoomTypes(newRoomTypes);
      }
    } catch (error) {
      console.error('Error loading rooms data:', error);
    } finally {
      setLoading(false);
    }
  };

  const filterRooms = () => {
    let filtered = rooms;

    if (searchTerm.trim()) {
      filtered = filtered.filter(room =>
        room.roomNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        room.type.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter) {
      filtered = filtered.filter(room => room.status === statusFilter);
    }

    if (typeFilter) {
      filtered = filtered.filter(room => room.type.id === typeFilter);
    }

    setFilteredRooms(filtered);
  };

  const handleAddRoom = () => {
    setSelectedRoom(undefined);
    setShowForm(true);
  };

  const handleEditRoom = (roomId: string) => {
    const room = rooms.find(r => r.id === roomId);
    setSelectedRoom(room);
    setShowForm(true);
  };

  const handleViewDetails = (roomId: string) => {
    const room = rooms.find(r => r.id === roomId);
    setSelectedRoom(room);
    setShowDetails(true);
  };

  const handleDeleteRoom = async (roomId: string) => {
    const room = rooms.find(r => r.id === roomId);
    if (!room) return;

    if (window.confirm(`Are you sure you want to delete Room ${room.roomNumber}?`)) {
      try {
        await roomService.deleteRoom(roomId);
        await loadData();
      } catch (error) {
        console.error('Error deleting room:', error);
        alert('Failed to delete room. Please try again.');
      }
    }
  };

  const handleUpdateStatus = async (roomId: string, newStatus: RoomStatus) => {
    try {
      await roomService.updateRoomStatus(roomId, newStatus);
      await loadData();
    } catch (error) {
      console.error('Error updating room status:', error);
      alert('Failed to update room status');
    }
  };

  const handleSaveRoom = async () => {
    setShowForm(false);
    setSelectedRoom(undefined);
    await loadData();
  };

  const handleCancelForm = () => {
    setShowForm(false);
    setSelectedRoom(undefined);
  };

  const handleCloseDetails = () => {
    setShowDetails(false);
    setSelectedRoom(undefined);
  };

  const handleEditFromDetails = () => {
    setShowDetails(false);
    setShowForm(true);
  };

  const handleUpdateStatusFromDetails = async (status: RoomStatus) => {
    if (selectedRoom) {
      await handleUpdateStatus(selectedRoom.id, status);
      // Update the selected room for the details modal
      const updatedRoom = rooms.find(r => r.id === selectedRoom.id);
      if (updatedRoom) {
        setSelectedRoom(updatedRoom);
      }
    }
  };
  
  const statusOptions = [{ value: '', label: 'All Statuses' }, ...Object.values(RoomStatus).map(s => ({ value: s, label: s }))];
  const typeOptions = [{ value: '', label: 'All Types' }, ...roomTypes.map(rt => ({ value: rt.id, label: rt.name }))];

  return (
    <div className="space-y-6">
      <Card>
        <div className="flex flex-col md:flex-row justify-between items-center gap-4 mb-6">
          <h2 className="text-2xl font-poppins font-semibold text-navy-dark dark:text-gold-accent">
            Room Management
          </h2>
          <Button
            onClick={handleAddRoom}
            leftIcon={<PlusIcon className="w-5 h-5"/>}
            variant="primary"
          >
            Add New Room
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6 p-4 bg-light-gray dark:bg-navy-light rounded-lg">
          <Input
            placeholder="Search by room number or type..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            icon={<SearchIcon className="w-5 h-5"/>}
          />
          <Select
            options={statusOptions}
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value as RoomStatus | '')}
            placeholder="Filter by status"
          />
          <Select
            options={typeOptions}
            value={typeFilter}
            onChange={(e) => setTypeFilter(e.target.value as string | '')}
            placeholder="Filter by type"
          />
        </div>

        <RoomList
          rooms={filteredRooms}
          onViewDetails={handleViewDetails}
          onEdit={handleEditRoom}
          onDelete={handleDeleteRoom}
          onUpdateStatus={handleUpdateStatus}
          loading={loading}
        />
      </Card>

      <RoomStatusLegend />

      {/* Room Form Modal */}
      <RoomForm
        room={selectedRoom}
        onSave={handleSaveRoom}
        onCancel={handleCancelForm}
        isOpen={showForm}
      />

      {/* Room Details Modal */}
      {selectedRoom && (
        <RoomDetails
          room={selectedRoom}
          onClose={handleCloseDetails}
          onEdit={handleEditFromDetails}
          onUpdateStatus={handleUpdateStatusFromDetails}
          isOpen={showDetails}
        />
      )}
    </div>
  );
};

export default RoomsPage;
