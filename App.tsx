
import React, { useState, useEffect } from 'react';
import { Routes, Route, useLocation } from 'react-router-dom';
import Sidebar from './components/layout/Sidebar';
import Header from './components/layout/Header';
import MainContent from './components/layout/MainContent';
import DashboardPage from './pages/DashboardPage';
import BookingsPage from './pages/BookingsPage';
import RoomsPage from './pages/RoomsPage';
import HousekeepingPage from './pages/HousekeepingPage';
import GuestsPage from './pages/GuestsPage';
import ReportsPage from './pages/ReportsPage';
import SettingsPage from './pages/SettingsPage';
import NotFoundPage from './pages/NotFoundPage';
import { UserRole } from './types';
import { useTheme } from './hooks/useThemeHook';
import { dataInitService } from './services/dataInitService';

const App: React.FC = () => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [currentUserRole, setCurrentUserRole] = useState<UserRole>(UserRole.Admin);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { theme } = useTheme();
  const location = useLocation();

  // Initialize database on app start
  useEffect(() => {
    const initApp = async () => {
      try {
        await dataInitService.initializeApp();
        setIsLoading(false);
      } catch (err) {
        setError('Failed to initialize application');
        setIsLoading(false);
        console.error('App initialization error:', err);
      }
    };

    initApp();
  }, []);

  useEffect(() => {
    document.documentElement.classList.toggle('dark', theme === 'dark');
  }, [theme]);

  // Close sidebar on small screens when navigating
  useEffect(() => {
    if (window.innerWidth < 768) {
      setIsSidebarOpen(false);
    }
  }, [location]);

  const toggleSidebar = () => setIsSidebarOpen(!isSidebarOpen);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen bg-background">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Initializing application...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-screen bg-background">
        <div className="text-center">
          <p className="text-destructive mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-light-gray dark:bg-navy-dark text-dark-gray dark:text-light-gray overflow-hidden">
      <Sidebar isOpen={isSidebarOpen} toggleSidebar={toggleSidebar} userRole={currentUserRole} />
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header 
          toggleSidebar={toggleSidebar} 
          isSidebarOpen={isSidebarOpen} 
          userRole={currentUserRole} 
          setUserRole={setCurrentUserRole}
        />
        <MainContent>
          <Routes>
            <Route path="/" element={<DashboardPage userRole={currentUserRole} />} />
            <Route path="/bookings" element={<BookingsPage userRole={currentUserRole} />} />
            <Route path="/rooms" element={<RoomsPage userRole={currentUserRole} />} />
            <Route path="/housekeeping" element={<HousekeepingPage userRole={currentUserRole} />} />
            <Route path="/guests" element={<GuestsPage userRole={currentUserRole} />} />
            <Route path="/reports" element={<ReportsPage userRole={currentUserRole} />} />
            <Route path="/settings" element={<SettingsPage userRole={currentUserRole} />} />
            <Route path="*" element={<NotFoundPage />} />
          </Routes>
        </MainContent>
      </div>
    </div>
  );
};

export default App;
