
import React from 'react';
import { NavLink } from 'react-router-dom';
import { NAVIGATION_ITEMS, APP_NAME } from '../../constants';
import { UserRole, NavItem } from '../../types';
import { KeyIcon, XIcon } from '../shared/Icon'; // KeyIcon for logo, XIcon for close

interface SidebarProps {
  isOpen: boolean;
  toggleSidebar: () => void;
  userRole: UserRole;
}

const Sidebar: React.FC<SidebarProps> = ({ isOpen, toggleSidebar, userRole }) => {
  const filteredNavItems = NAVIGATION_ITEMS.filter(item => item.allowedRoles.includes(userRole));

  return (
    <>
      {/* Overlay for mobile */}
      {isOpen && <div onClick={toggleSidebar} className="fixed inset-0 z-30 bg-black opacity-50 md:hidden"></div>}

      <aside
        className={`fixed inset-y-0 left-0 z-40 flex flex-col w-64 bg-navy dark:bg-slate-900 text-light-gray transform ${
          isOpen ? 'translate-x-0' : '-translate-x-full'
        } md:relative md:translate-x-0 transition-transform duration-300 ease-in-out shadow-lg`}
      >
        <div className="flex items-center justify-between h-20 px-6 border-b border-navy-light dark:border-slate-700">
          <div className="flex items-center">
            <KeyIcon className="w-8 h-8 text-gold-accent mr-3" />
            <span className="text-2xl font-poppins font-semibold text-white">{APP_NAME}</span>
          </div>
          <button onClick={toggleSidebar} className="md:hidden text-light-gray hover:text-gold-accent">
            <XIcon className="w-6 h-6" />
          </button>
        </div>

        <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
          {filteredNavItems.map((item: NavItem) => (
            <NavLink
              key={item.name}
              to={item.path}
              className={({ isActive }) =>
                `flex items-center px-4 py-3 rounded-lg transition-colors duration-150 ease-in-out group
                ${
                  isActive
                    ? 'bg-gold-accent text-navy-dark font-semibold shadow-md'
                    : 'text-gray-300 hover:bg-navy-light dark:hover:bg-slate-700 hover:text-white'
                }`
              }
            >
              {({ isActive }) => (
                <>
                  <item.icon className={`w-5 h-5 mr-3 transition-colors duration-150 ease-in-out ${isActive ? 'text-navy-dark' : 'text-gray-400 group-hover:text-white'}`} />
                  <span>{item.name}</span>
                </>
              )}
            </NavLink>
          ))}
        </nav>

        <div className="px-6 py-4 mt-auto border-t border-navy-light dark:border-slate-700">
          <p className="text-xs text-center text-gray-400">
            &copy; {new Date().getFullYear()} {APP_NAME}
          </p>
        </div>
      </aside>
    </>
  );
};

export default Sidebar;