
import React from 'react';
import { UserRole } from '../types';
import Card from '../components/shared/Card';
import Input from '../components/shared/Input';
import Button from '../components/shared/Button';
// import Select from '../components/shared/Select'; // For managing room types or staff roles

interface SettingsPageProps {
  userRole: UserRole;
}

const SettingsPage: React.FC<SettingsPageProps> = ({ userRole }) => {
  // State hooks must be called before any early returns
  const [hotelName, setHotelName] = React.useState('Premium Stay Hotel');
  const [hotelAddress, setHotelAddress] = React.useState('123 Luxury Ave, Comfort City');
  const [defaultTaxRate, setDefaultTaxRate] = React.useState('10'); // Percentage

  if (userRole !== UserRole.Admin) {
    return (
      <Card>
        <p className="text-center text-status-red p-8">You do not have permission to view this page.</p>
      </Card>
    );
  }

  const handleSaveChanges = () => {
    alert('Settings saved (mock)!');
  };

  return (
    <div className="space-y-8">
      <h1 className="text-3xl font-poppins font-semibold text-navy-dark dark:text-gold-accent">System Settings</h1>

      <Card title="Hotel Information">
        <div className="space-y-4">
          <Input 
            label="Hotel Name" 
            id="hotelName" 
            value={hotelName} 
            onChange={(e) => setHotelName(e.target.value)} 
          />
          <Input 
            label="Hotel Address" 
            id="hotelAddress" 
            value={hotelAddress} 
            onChange={(e) => setHotelAddress(e.target.value)} 
          />
          <div>
            <label htmlFor="hotelLogo" className="block text-sm font-medium text-dark-gray dark:text-light-gray mb-1">Hotel Logo</label>
            <Input type="file" id="hotelLogo" className="p-0 file:mr-4 file:py-2 file:px-4 file:rounded-l-lg file:border-0 file:text-sm file:font-semibold file:bg-navy-light file:text-gold-accent hover:file:bg-navy dark:file:bg-navy dark:file:text-gold-accent dark:hover:file:bg-navy-light" />
          </div>
        </div>
      </Card>

      <Card title="Room Type Management">
        <p className="text-gray-600 dark:text-gray-300 mb-4">Manage room types, pricing, and amenities.</p>
        {/* Placeholder for room type list and add/edit forms */}
        <div className="h-40 flex items-center justify-center bg-light-gray dark:bg-navy-light rounded-lg">
            <p className="text-gray-500 dark:text-gray-400">Room type management interface coming soon.</p>
        </div>
        <Button variant="primary" className="mt-4">Add New Room Type</Button>
      </Card>

      <Card title="Staff Accounts & Permissions">
        <p className="text-gray-600 dark:text-gray-300 mb-4">Manage staff accounts and their access levels.</p>
        {/* Placeholder for staff list and role management */}
         <div className="h-40 flex items-center justify-center bg-light-gray dark:bg-navy-light rounded-lg">
            <p className="text-gray-500 dark:text-gray-400">Staff management interface coming soon.</p>
        </div>
        <Button variant="primary" className="mt-4">Add New Staff Member</Button>
      </Card>

      <Card title="Financial Settings">
        <div className="space-y-4">
          <Input 
            label="Default Tax Rate (%)" 
            id="taxRate" 
            type="number"
            value={defaultTaxRate} 
            onChange={(e) => setDefaultTaxRate(e.target.value)} 
          />
          <p className="text-gray-600 dark:text-gray-300">Payment Gateway Integration: (ConnectStripe / ConnectPayPal buttons placeholder)</p>
          <div className="flex space-x-2">
            <Button variant="secondary">Connect Stripe</Button>
            <Button variant="secondary">Connect PayPal</Button>
          </div>
        </div>
      </Card>
      
      <div className="flex justify-end mt-8">
        <Button variant="primary" size="lg" onClick={handleSaveChanges}>
          Save All Changes
        </Button>
      </div>
    </div>
  );
};

export default SettingsPage;
